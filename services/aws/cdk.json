{"app": "npx ts-node --prefer-ts-exts bin/app.ts", "context": {"@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-apigateway:disableCloudWatchRole": true, "@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId": true, "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021": true, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/aws-lambda:recognizeLayerVersion": true, "@aws-cdk/aws-lambda:recognizeVersionProps": true, "@aws-cdk/aws-rds:lowercaseDbIdentifier": true, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": true, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/core:enablePartitionLiterals": true, "@aws-cdk/core:stackRelativeExports": true, "@aws-cdk/core:target-partitions": ["aws", "aws-cn"], "@aws-cdk/core:validateSnapshotRemovalPolicy": true}, "watch": {"exclude": ["README.md", "cdk*.json", "**/*.d.ts", "**/*.js", "tsconfig.json", "package*.json", "pnpm-lock.yaml", "node_modules", "test"], "include": ["**"]}}