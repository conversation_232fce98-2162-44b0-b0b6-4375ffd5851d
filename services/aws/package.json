{"bin": {"malou-serverless": "bin/app.ts"}, "dependencies": {"@aws-cdk/aws-lambda-python-alpha": "^2.194.0-alpha.0", "@malou-io/package-config": "workspace:*", "aws-cdk": "^2.1013.0", "aws-cdk-lib": "^2.194.0", "constructs": "^10.4.2", "dotenv": "^16.5.0", "source-map-support": "^0.5.21", "zod": "^3.24.4"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/eslint": "^8.48.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.12", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "esbuild": "^0.25.4", "eslint": "^8.48.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "jest": "^29.7.0", "prettier": "^3.5.3", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typescript": "^5.8.3"}, "name": "@malou-io/aws-services", "packageManager": "pnpm@10.6.5", "private": true, "scripts": {"build": "tsc -b", "deploy": "pnpm run build && cdk deploy --all", "diff": "pnpm run build && cdk diff", "format": "prettier --write \"**/*.{ts,js,md}\"", "format:check": "prettier \"**/*.{ts,js,md}\" --check", "lint": "eslint \"**/*.ts\"", "lint-fix": "eslint \"**/*.ts\" --fix", "lint-staged": "lint-staged --no-stash", "synth": "pnpm run build && cdk synth", "test": "jest"}, "version": "0.0.0"}