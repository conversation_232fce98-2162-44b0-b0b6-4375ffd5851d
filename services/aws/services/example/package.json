{"author": "", "dependencies": {"@malou-io/package-dto": "workspace:*", "@malou-io/package-utils": "workspace:*", "aws-cdk-lib": "^2.194.0", "aws-lambda": "^1.0.7", "constructs": "^10.4.2"}, "description": "lambda that demonstrates monorepo setup", "devDependencies": {"@types/aws-lambda": "^8.10.149", "@types/jest": "^29.5.14", "esbuild": "^0.25.4", "jest": "^29.7.0"}, "license": "MIT", "main": "./src/lambda.ts", "name": "@malou-io/lambda-example", "private": true, "version": "1.0.0"}