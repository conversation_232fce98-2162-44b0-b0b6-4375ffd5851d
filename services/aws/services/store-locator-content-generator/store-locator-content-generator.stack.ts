import { <PERSON>Function, PythonLayerVersion } from '@aws-cdk/aws-lambda-python-alpha';
import { CfnOutput, Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import path from 'path';

import { config } from ':config';

export class StoreLocatorContentGeneratorStack extends Stack {
    constructor(scope: Construct, id: string, props?: StackProps) {
        super(scope, id, props);

        const { openaiProviderLayer, anthropicProviderLayer, utilsLayer } = this._buildLayers();

        this._buildService({ layers: { openaiProviderLayer, anthropicProviderLayer, utilsLayer } });
    }

    private _buildService({
        layers,
    }: {
        layers: { openaiProviderLayer: PythonLayerVersion; anthropicProviderLayer: PythonLayerVersion; utilsLayer: PythonLayerVersion };
    }) {
        const storeLocatorContentGenerator = new PythonFunction(this, `StoreLocatorContentGenerator${config.appSuffix}`, {
            entry: path.join(__dirname),
            index: 'app.py',
            handler: 'handler',
            runtime: Runtime.PYTHON_3_12,
            timeout: Duration.seconds(60),
            functionName: `storeLocatorContentGenerator${config.appSuffix}`,
            layers: [layers.openaiProviderLayer, layers.utilsLayer, layers.anthropicProviderLayer],
            environment: {
                OPENAI_API_KEY: config.openaiApiKeys.storeLocatorContentGenerator,
                ANTHROPIC_API_KEY: config.anthropicApiKeys.storeLocatorContentGenerator,
            },
        });

        new CfnOutput(this, 'StoreLocatorContentGeneratorFunctionName', {
            value: storeLocatorContentGenerator.functionName,
            description: 'Store Locator Content Generator Lambda function',
        });
    }

    private _buildLayers() {
        const openaiProviderLayer = new PythonLayerVersion(this, 'openaiUtilitiesLayer', {
            entry: path.join(__dirname, '../../layers/python/openai_provider'),
            compatibleRuntimes: [Runtime.PYTHON_3_12],
            description: 'Shared python utils',
            layerVersionName: 'openaiProviderLayer',
        });

        const anthropicProviderLayer = new PythonLayerVersion(this, 'anthropicUtilitiesLayer', {
            entry: path.join(__dirname, '../../layers/python/anthropic_provider'),
            compatibleRuntimes: [Runtime.PYTHON_3_12],
            description: 'Shared python utils',
            layerVersionName: 'anthropicProviderLayer',
        });

        const utilsLayer = new PythonLayerVersion(this, 'utilsLayer', {
            entry: path.join(__dirname, '../../layers/python/utils'),
            compatibleRuntimes: [Runtime.PYTHON_3_12],
            description: 'Shared python utils',
            layerVersionName: 'utilsLayer',
        });

        return {
            openaiProviderLayer,
            anthropicProviderLayer,
            utilsLayer,
        };
    }
}
