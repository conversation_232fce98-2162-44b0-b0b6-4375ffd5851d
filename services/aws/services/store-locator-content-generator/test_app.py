import pytest
from unittest.mock import MagicMock
from app import handler
from core.models.event_model import RequestTypeEnum, RequestRelatedEntityCollectionEnum
from aws_lambda_powertools.utilities.typing import LambdaContext
import json
import os

BASE_PAYLOAD = {
    "relatedEntityCollection": RequestRelatedEntityCollectionEnum.STORE_LOCATOR,
    "restaurantData": {
        "restaurantName": "McDonald's SouthBay Anytown",
        "language": "fr",
        "organizationName": "McDonald's",
        "address": {"locality": "123 Main St, Anytown", "postalCode": "12345"},
        "keywords": [
            "fast food restaurant",
            "burgers Anytown",
            "family restaurant",
            "drive-thru Anytown",
            "restaurant drive-thru",
            "fast food Anytown",
            "Burgers and fries",
        ],
        "brandTone": ["friendly", "casual"],
        "targetAudience": ["families", "young adults"],
        "specificsDirectives": ["highlight drive-thru service"],
        "restaurantOffers": ["20% off on first order"],
        "restaurantContext": ["new store opening soon"],
        "context": [
            {
                "Title H1": "La nouvelle expérience McDonald's à Anytown",
            }
        ],
    },
}


def append_result(result, output_file="output.json"):
    # Read existing data if file exists
    if os.path.exists(output_file):
        with open(output_file, "r", encoding="utf-8") as f:
            try:
                data = json.load(f)
                if not isinstance(data, list):
                    data = [data]
            except json.JSONDecodeError:
                data = []
    else:
        data = []

    data.append(result)

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def test_handler_restaurant_page_url_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.RESTAURANT_RESTAURANT_PAGE_URL_GENERATION
    result = handler(event, context)
    append_result(result)

    assert result is not None


def test_handler_h1_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.H1_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_gallery_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.GALLERY_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_gallery_block_subtitle_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.GALLERY_BLOCK_SUBTITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_head_meta_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.HEAD_META_DESCRIPTION_GENERATION
    result = handler(event, context)
    assert result is not None


def test_handler_head_meta_twitter_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.HEAD_META_TWITTER_DESCRIPTION_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_organization_keywords_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.ORGANIZATION_KEYWORDS_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_reviews_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.REVIEWS_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_social_media_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_cta_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.CTA_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None
