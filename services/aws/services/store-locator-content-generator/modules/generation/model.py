from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field, model_validator

AI_MAX_RETRIES = 3

CONTEXT_PROMPTS = {
    "restaurant_page_url_generation": """
    Generate a structured website URL object using the fields:

- specialty: the main food or cuisine type of the restaurant
- location: Locality or location with thr district number if available.

# Goal:
Use the restaurant’s `keywords` and `address` to extract a **specialty** and a **location** that will form the components of a website URL. Return these components as a dictionary with the keys: `specialty` and `location`.

# Rules:

- **Specialty extraction:**
  - Analyze the provided `keywords` to identify the term that best represents the restaurant’s specialty.
  - Prioritize specificity and relevance to food or cuisine (e.g., “cuisine italienne”, “vegan”, “ramen”).
  - Use the indicated language to ensure the output matches the audience’s expectations.
  - Translate the specialty if needed (e.g., `["cuisine italienne"]` with language "English" → specialty = "italian-cuisine").
  - Avoid the word "restaurant" in the specialty.

- **Location extraction:**
  - Use the given address as the basis for the location.
  - Keep district numbers or postal codes if present (e.g., “Paris 4”, “75009 Paris” → "paris-4" or "paris-9").
  - Normalize spaces and accents; use lowercase and tildes (-) instead of spaces.

# Output Format:

- Return a dictionary with two keys: `specialty` and `location`
- Both values must:
  - Be lowercase
  - Use tildes (-) instead of spaces
  - Avoid redundancy or overly long phrases
  - Contain no slashes or special characters

# Examples

**Example 1**  
Input:
- Address: Paris 4  
- Keywords: ["Bio", "vegan", "burgers"]  
- Language: English

Output:
```json
{
  "specialty": "bio-vegan",
  "location": "paris-4"
}

**Example 2**  
Input:  
- Address: Miami Beach  
- Keywords: Seafood, ocean view, fresh 
- Language : French


Output:
```json
{
  "specialty": "fruits-de-mer",
  "location": "miami-beach"
}

""",
    "h1_title_generation": """
    Create a title for your restaurant's website header using the provided keywords, restaurant's name, speciality and location.

# Rules :
- Extract the speciality from the keywords, the restaurant base name from the restaurant name and the location from the address : 
    **Restaurant base name extraction** : 
        If restaurant Name contains speciality or location, extract the restaurant base name only to not create redundancy.
        Examples :
        - restaurant Name : "Mamika sandwich place Paris" -> Contains speciality "sandwish place" and location "Paris" -> Extracted element for restaurant Name= "Mamika".
        - restaurant Name : "Restaurant Ommar Marseille street food" -> restaurant base name = "Ommar" 
                 
    **Specialty extraction** : 
        Analyse the list of keywords and extract the one that most describes the speciality of the restaurant and use that as speciality in the indicated language.
        Example : ['Cuisine italienne','pizza','pizza paris4'] -> speciality="cuisine italienne"
    **Location extraction**:
        Use the address as a location. Keep the district code if present.
        Example : Address : Paris 9 -> location= paris 9

- Make sure the title is in the indicated language :
    **Translate the content if necessary**:
        Example : keywords :["cuisine italienne"] language : "English" -> speciality="Italian cuisine"

- Make sure the title is worded correctly and in the rigth format :
  **size**: Between 50 and 60 characters. If the title is shorter than 50 characters or longer than 60 characters, refer to the keywords to use a shorter/longer speciality wording or a shorter/longer location wording.
  **Format**: No bullet points, periods and hashtags. The title should be enganging and serves the SEO of the website.
  **Language**: Ensure the title is in the indicated language.


# Steps:
1. Extract the necessary information to generate the title.
2. Assert that the information adheres to the rules above.
3. Craft the title and assert it fits the wording constraints.
4. In case the title doesn't fit the size requirements, adjust the information by using longer/shorter speciality or location wording.


# Output Format

- The title length should be between 50 and 60 characters.
- Ensure the output is in the indicated language.

# Examples

**Example 1:**

- **Input:**
  - Restaurant Name: "Le Délicieux"
  - Keywords: "French gastronomy, chef's specialties"
  - Location: "Lyon"

- **Output:**
  - "Le Délicieux - French Gastronomy in Lyon"

**Example 2:**

- **Input:**
  - Restaurant Name: "Le Gourmet"
  - Keywords: "Italian cuisine, daily menu"
  - Location: locality : "Paris", district: "9"

- **Output:**
  - "Le Gourmet - Italian Cuisine in Paris 9"

    
  
""",
    "head_meta_description_generation": """Create a Meta description for a restaurant's website that is between 140 and 160 characters long. The description should include strategic keywords, the restaurant's address if possible, and a call-to-action.

# Steps

1. Identify strategic keywords related to the restaurant, such as cuisine type, unique features, or specialties.
2. Incorporate the restaurant's address in a concise manner.
3. Analyse the context of previous generations to ensure consistency and avoid repetition.
4. Craft a compelling call-to-action that encourages visitors to engage with the website or visit the restaurant.
5. Ensure the total length is within 140 to 160 characters.

# Output Format

A single sentence meta description with a length of 140 to 160 characters, incorporating strategic keywords, the address if possible, and a call-to-action. 

# Notes

- Consider the restaurant's unique selling points to highlight in the keywords.
- Ensure the address is presented succinctly to save character space.
- Ensure the output is in the indicated language.
""",
    "head_meta_twitter_description_generation": """Create a Meta description for a restaurant's website that is between 140 and 160 characters long. The description should include strategic keywords, the restaurant's address if possible, and a call-to-action.

# Steps

1. Identify strategic keywords related to the restaurant, such as cuisine type, unique features, or specialties.
2. Incorporate the restaurant's address in a concise manner.
3. Craft a compelling call-to-action that encourages visitors to engage with the website or visit the restaurant.
4. Ensure the total length is within 140 to 160 characters.

# Output Format

A single sentence meta description with a length of 140 to 160 characters, incorporating strategic keywords, the address if possible, and a call-to-action. 

# Notes

- Consider the restaurant's unique selling points to highlight in the keywords.
- Ensure the address is presented succinctly to save character space.
- Ensure the output is in the indicated language.
""",
    "gallery_block_title_generation": """Generate a title for photos on a restaurant's website using the provided title and information, such as restaurant name and keywords. 
    
# Instructions : 
- Ensure the title is coherent with the provided information while avoiding repetition of words or expressions. 
- The title should integrate important keywords that enhance SEO and attract users. 
- It should remain natural, useful, and inviting. Focus on specific elements like ingredients, preparation, restaurant values (e.g., organic, local), or unique characteristics, avoiding vague terms like "authentic" or "unique". Do not invent details. 
- Limit the title to a maximum of 60 characters. 
- Analyse the context of previous generations to ensure consistency and avoid repetition. 
- Ensure the output is in the indicated language.

# Steps : 
1. **Analyze Input Information**: Identify all key elements such as restaurant name, keywords, and any specific directives provided.
2. **SEO Optimization**: Ensure the title is optimized for search engines by incorporating relevant keywords naturally.
3. **Avoid Repetition**: Ensure that the title does not repeat words or phrases from the provided information, maintaining freshness and appeal.
4. **Craft Title**: Use the identified elements to create a compelling and engaging title that reflects the essence of the restaurant and its offerings.

# Output Format

- A coherent title under 60 characters enhancing the given information with keywords and specific elements, avoiding repetition.
""",
    "gallery_block_subtitle_generation": """Generate a subtitle for photos on your restaurant's website using the provided title and information, such as restaurant name and keywords. 

# Instructions :
- Ensure the subtitle is coherent with the provided title while avoiding repetition of words or expressions. 
- The subtitle should complement the title by integrating important keywords that enhance SEO and attract users. It should remain natural, useful, and inviting. Focus on specific elements like ingredients, preparation, restaurant values (e.g., organic, local), or unique characteristics, avoiding vague terms like "authentic" or "unique". 
- Do not invent information. 
- Limit the subtitle to a maximum of 80 characters.
- Analyse the context of previous generations to ensure consistency and avoid repetition : The title and subtitle should work together to create a cohesive message without repeating elements.

Steps : 
1. **Analyze Input Information**: Identify all key elements such as restaurant name, keywords, and any specific directives provided.
2. **SEO Optimization**: Ensure the subtitle is optimized for search engines by incorporating relevant keywords naturally.
3. **Avoid Repetition**: Ensure that the subtitle does not repeat words or phrases from the provided title, maintaining freshness and appeal.
4. **Craft Subtitle**: Use the identified elements to create a compelling and engaging subtitle that reflects the essence of the restaurant and its offerings.

# Output Format

- A coherent subtitle under 80 characters enhancing the given title with keywords and specific elements, avoiding repetition.
# Notes
- Ensure the output is in the indicated language.
""",
    "description_block_generation": """Create creative and engaging content for a restaurant's website. The goal is to boost client engagement and improve SEO. Two blocks of information will be created, each block should include two sections with their respective subtitles and paragraphs. Ensure that the content incorporates specific details provided, including tone, target audience, address, restaurant name, keywords, unique offerings, and features.

# Steps:

1. **Analyze Input Information**:
   Extract all key elements such as tone, target audience, address, restaurant name, keywords, unique offerings, and features.

2. **Define Subjects**:
   For each block, define a distinct subject to avoid repetition. Each block should tackle a different aspect (e.g., one could focus on offerings, the other on features or location).

3. **Craft Titles and Subtitles**:
   Create compelling and relevant titles and subtitles that reflect the content and the restaurant’s identity and offerings.

4. **Compose Content**:

   * Focus on **Client Engagement**: Use a tone and style that resonates with the target audience.
   * **SEO Optimization**: Incorporate the provided keywords naturally to aid SEO.
   * **Highlight Unique Offerings**: Emphasize specific features and offerings that differentiate the restaurant.
   * Ensure that each section contains a paragraph of approximately **100-150 words**.

5. **Output Format**:
   Return the content in the following format, which includes **Blocks** and **Sections**:

```python
class Section(BaseModel):
    subtitle: str
    text: str

class Block(BaseModel):
    title: str
    sections: List[Section]

class BlockGenerated(BaseModel):
    blocks: List[Block]
```

### Example Input:

* **Tone & Audience**: Friendly, Family-oriented
* **Restaurant Name**: The Cozy Corner
* **Address**: 123 Main St, Springfield
* **Keywords**: Family dining, cozy atmosphere, homemade meals
* **Unique Offerings**: Home-cooked breakfast, outdoor seating
* **Language**: EN

### Example Output:
```python
BlockGenerated(
    blocks=[
        Block(
            title="Welcome to The Cozy Corner",
            sections=[
                Section(
                    subtitle="A Family Dining Experience Like No Other",
                    text="Located at 123 Main St, Springfield, The Cozy Corner is your perfect destination for family dining. With a cozy atmosphere that feels like home, we bring families together with hearty home-cooked meals. Our diverse menu offers everything from comforting breakfasts to delicious dinners, made with the freshest ingredients. Whether you’re enjoying a leisurely breakfast outdoors or celebrating a special occasion with loved ones, we ensure every visit is filled with warmth and joy."
                ),
                Section(
                    subtitle="Savor Homemade Meals in a Cozy Atmosphere",
                    text="At The Cozy Corner, we take pride in serving homemade meals that remind you of family gatherings. Our home-cooked breakfasts are a highlight, featuring everything from fluffy pancakes to savory egg dishes. The cozy, inviting atmosphere enhances your dining experience, making every meal memorable. Plus, with our outdoor seating, you can enjoy a beautiful Springfield morning or evening while savoring your favorite dishes. Come see why The Cozy Corner is more than just a restaurant—it’s where families make lasting memories."
                )
            ]
        ),
        Block(
            title="Why Choose The Cozy Corner?",
            sections=[
                Section(
                    subtitle="A Cozy Atmosphere for Every Family",
                    text="The Cozy Corner is more than a place to eat—it's a space where families gather to share moments and meals. We understand that dining out should be about more than just food, which is why we’ve created an environment that’s perfect for bonding. From the warm, homely décor to the friendly staff, everything here is designed to make you feel comfortable and welcomed. Whether you're here for a quick bite or a leisurely meal, you’ll find that our cozy atmosphere invites relaxation and togetherness."
                ),
                Section(
                    subtitle="Seasonal Specials & Outdoor Seating",
                    text="Every season, The Cozy Corner unveils new specials that highlight the best of local ingredients and seasonal flavors. Our outdoor seating is perfect for enjoying these dishes while basking in the beautiful Springfield weather. Whether you're grabbing a quick brunch or savoring a long dinner with the family, our outdoor terrace provides the perfect backdrop for a memorable dining experience. Join us today to discover the changing tastes of each season and enjoy the comfort of dining al fresco."
                )
            ]
        )
    ]
)
```

### Notes:

* **Generate the content in the specified language**.
* **Ensure that each block covers a distinct subject without repetition**.
* **Be as creative and engaging as possible**.
* **Aim for a natural flow of content, avoiding robotic keyword usage**.

""",
    "organization_keywords_generation": """Identify and select three keywords from a list of restaurant-related keywords that best describe the identity of your restaurant chain for use on your website to enhance engagement and boost SEO. One of the chosen keywords must reflect the principal product of your restaurant chain.

Consider the uniqueness of your restaurant chain, what makes it stand out, and how it aligns with customer expectations. The chosen keywords should reflect the essence of your brand while being relevant to your target audience and conducive to SEO.

# Steps

1. **Analyze Keywords**: Evaluate each provided keyword for relevance to the restaurant chain’s identity, uniqueness, alignment with customer interests, and ensure one keyword reflects the principal product.
2. **Rank Keywords**: Rank the keywords based on their alignment with your brand identity, popularity, and potential for SEO impact.
3. **Select Keywords**: Choose the top three keywords that most effectively capture the essence of your restaurant brand and align with SEO goals, ensuring one represents the principal product.

# Output Format

The output should be a list of three keywords formatted as simple text, e.g. "Keyword1, Keyword2, Keyword3".

# Examples

**Input:**  
Keywords: ["cozy", "organic", "local", "sustainable", "gourmet", "family-friendly", "coffee"]

**Output:**  
"cozy, gourmet, coffee" 

**(Be sure to choose keywords that describe your restaurant's unique selling propositions and are likely to attract and engage your target audience.)**

# Notes
- Ensure the output is in the indicated language.
- Consider competitive analysis on similar restaurants or industry trends to ensure the selected keywords are unique yet effective.
- Keep in mind the balance between industry-specific terms and more generalized descriptors.
- The keywords should embody the core values and mission of your restaurant chain while resonating with potential customers, with one reflecting the principal product.""",
    "reviews_block_title_generation": """Craft a compelling, trustworthy, and engaging **headline** to introduce the customer reviews section on your restaurant's website. Avoid generic or overused terms like “testimonials.” The title should reflect your restaurant’s **unique character**, **specialties**, and **brand tone**, while emphasizing the **authentic experiences** of your customers.


### Instructions

1. **Gather Brand Data**
   Collect the following details about your restaurant:
   * **Specialties** (e.g., type of cuisine or signature dishes)
   * **Ambiance** (e.g., cozy, romantic, modern, festive)
   * **Tone** (e.g., elegant, playful, casual, bold)
   * **Target Audience** (e.g., families, foodies, tourists, locals, professionals)
   * **Keywords** that describe your values, food, or experience (e.g., “authentic,” “fresh,” “comfort,” “indulgence”)

2. **Inspire Trust and Emotion**
   Your title should inspire confidence, warmth, and curiosity—reflecting the real experiences and delight of your customers.

3. **Avoid Overused Terms**
   Don’t use terms like “Testimonials” or “Customer Feedback.” Instead, opt for a more creative, brand-aligned expression.

4. **Craft the Title**
   Create one engaging sentence (or sentence fragment) that introduces the reviews section in a unique and resonant way.

---

### Output Format

Provide a **single, catchy headline** that reflects the brand identity, evokes positive emotion, and welcomes visitors to explore customer reviews.

---

### Examples

**Input:**

* Specialties: *Viennoiseries, tartes aux fruits*
* Ambiance: *Chaleureuse, artisanale*
* Tone: *Élégant et convivial*
* Target: *Amateurs de douceurs, touristes, familles*
* Keywords: *raffiné, authentique, fait maison*

**Output:**

* *“Mots Sucrés : Ce que disent nos gourmands”*

---

**Input:**

* Specialties: *Tacos, enchiladas, margaritas*
* Ambiance: *Festive, colorée*
* Tone: *Chaleureux et vibrant*
* Target: *Jeunes adultes, groupes d’amis, foodies*
* Keywords: *fiesta, épices, fraîcheur*

**Output:**

* *“Spice & Stories: Guests Share Their Fiesta Moments”*

---

**Input:**

* Specialties: *Ramen, udon, gyoza*
* Ambiance: *Moderne, zen*
* Tone: *Minimaliste et raffiné*
* Target: *Millennials, amateurs de cuisine japonaise*
* Keywords: *umami, traditionnel, savoureux*

**Output:**

* *“Noodle Narratives: Guests Share Their Slurpy Stories”*

---

**Input:**

* Specialties: *Cuisine végétarienne et bio*
* Ambiance: *Calme, verdoyante*
* Tone: *Naturel et inspirant*
* Target: *Flexitariens, éco-responsables, familles*
* Keywords: *fraîcheur, bien-être, local*

**Output:**

* *“Ce que pensent nos convives : Une aventure verte”*

---

**Input:**

* Specialties: *Burgers gourmets, frites maison*
* Ambiance: *Industrielle et branchée*
* Tone: *Jeune et audacieux*
* Target: *Étudiants, jeunes actifs, amateurs de street food*
* Keywords: *artisanal, généreux, fait-main*

**Output:**

* *“Bite into the Buzz: What Our Customers Are Raving About”*

# Notes
- Ensure the output is in the indicated language.
- Ensure the title resonates well with the intended audience, emphasizing trust and positive customer experiences.
- Adjust the tone to match the specific vibe and atmosphere you aim to convey with your restaurant's overall branding.""",
    "social_media_block_title_generation": """Create a catchy and engaging title to introduce the social media posts section for your restaurant. Ensure it aligns closely with the tone and format used in the customer reviews section. Utilize elements like your restaurant's specialties, ambiance, brand tone, and target audience to guide the creation.

# Steps

1. **Gather Social Data**: Collect essential social media attributes such as trending themes, engagement style, platform demographics, and keywords relevant to your restaurant.
2. **Engagement and Appeal**: Make sure the title is appealing and engaging. It should capture attention and resonate with the kind of conversations your audience expects on social media.
3. **Consistency**: Ensure that the title aligns with your overall branding and feels consistent with the style used for the customer reviews section.
4. **Analyze Previous Generations**: Review the context of previous generations to ensure the title is fresh and does not repeat elements unnecessarily.
5. **Draft Title**: Creatively integrate the collected elements to develop a compelling title that captures the energy and appeal of your restaurant on social media.

# Output Format

The output should be a single, catchy headline that encapsulates the essence of your restaurant's brand, specialties, and social media engagement.

# Examples

**Input:**
- Specialties: [Specialties]
- Ambiance: [Ambiance]
- Tone: [Tone]
- Target: [Target]
- Keywords: [Keywords]

**Output:**
- "Get Social : Dive Into [Specialties] and [Ambiance]!"

(For real examples, replace placeholders like [Specialties], etc., with the specific data pertinent to your restaurant.)

# Notes
- Ensure the output is in the indicated language.
- Ensure the title resonates well with your social media audience, emphasizing engagement and consistent brand themes.
- Tailor the tone to fit the dynamic nature of social media interactions while maintaining brand coherence.""",
    "cta_block_title_generation": """Create a catchy H2 title in my restaurant's website, positioned above three platform button links like "Ubereats" or "Deliveroo". 


# Steps

1. **Understand Brand Style**: Familiarize yourself with the brand’s style and tone to ensure the title reflects them accurately.
2. **Identify Core Message**: Determine the core message you wish to convey with the title that resonates with both the buttons below and the overall brand presence.
3. **Generate Ideas**: Draft several potential titles that meet the word count requirement ( 3 to 6 words ) and align with the brand's tone.
4. **Evaluate Options**: Review the drafted titles to ensure they are compelling and fitting.
5. **Finalize Title**: Choose the most suitable title.

# Output Format

The output should be a single, attractive headline comprising 3 to 6 words, aligned with the brand’s tone.

# Examples


Input:
Style: Modern, minimal
Tone: Cool and confident
Langauge : EN

Output:
"Crave It. Click It. Eat."

Input : 
Style: Family-friendly, cute
Tone: Sweet and joyful

Output:
"Big Flavor in Every Bite"

Input:

Style: Local, authentique
Tone: Chaleureux et traditionnel
Language : FR

Output:
"Les Saveurs local à domicile"

# Notes
- Ensure the output is in the indicated language.
- Ensure the title visually and contextually complements the buttons, maintaining cohesiveness with the brand’s online identity.""",
    "map_block_title_generation": """
Generate a title for the restaurant's store locator map page.

# Instructions:
- The title should be clear and concise.
- It should include the name of the restaurant group and the page's purpose (finding nearby restaurants or stores).
- Make sure the title is optimized for search engines by keeping it between 50 to 60 characters.
- Ensure that the title is engaging and matches the tone of the restaurant group's branding.

# Output Format:
- The title should be in the following format:
  "Find Our Restaurants | [Restaurant Group Name]" or "Find Our Stores | [Restaurant Group Name]"
- Keep the character count between 50 and 60 characters.
""",
    "map_description_generation": """
Generate a meta description for the restaurant's store locator map page.

# Instructions:
- The description should be between 150 and 160 characters long.
- Include key terms like "restaurant," "interactive map," and "find."
- Mention the restaurant group name and describe the interactive map's functionality.
- The description should encourage users to explore the map to find the closest restaurant or store.

# Output Format:
- Provide a single sentence description that is clear, concise, and SEO-friendly.
""",
    "map_twitter_description_generation": """
Generate a concise and engaging Twitter meta description for the restaurant's store locator map page.

# Instructions:
- The description should be between 140 and 160 characters.
- Focus on providing a quick and enticing overview of the page's purpose (finding nearby restaurants/stores).
- Mention the restaurant group and emphasize the interactive map feature.
- Make sure the description encourages users to click and explore the map.

# Output Format:
- Provide a single sentence description that highlights the interactive map and encourages user interaction.
""",
    "map_keywords_generation": """
Generate a list of SEO-friendly keywords for the restaurant's store locator map page.

# Instructions:
- Select a few relevant keywords that enhance the SEO of the store locator page.
- Include terms like "restaurant," "interactive map," "find restaurant," "restaurant near me," and the name of the restaurant group.
- Consider including geographical terms if possible to help with local SEO.

# Output Format:
- Provide a list of 5 to 10 keywords, formatted as plain text.
- Example: "restaurant, interactive map, find restaurant, restaurant near me, [Restaurant Group Name], nearby restaurants, [Location], [Restaurant Group Name] restaurant."
""",
    "h1_title_optimization": """Enhance the previously generated H1 title for your restaurant's website header using the provided keywords, restaurant name, speciality, location, and the previous AI-generated title. The goal is to improve SEO, engagement, and brand alignment.

# Rules:
1. **Use the Previous Title**: Start with the previously generated title and refine it.
2. **Adjust Length**: Ensure the title is between **50-60 characters**. Modify the speciality or location wording if needed.
3. **SEO & Engagement**: Make sure the title includes the restaurant name, speciality, and location in an SEO-friendly, natural way. Avoid redundancy.
4. **Brand Tone**: Keep the title aligned with the restaurant’s tone (casual or refined).
5. **No Punctuation**: Ensure no bullet points, hashtags, or periods are used.

# Steps:
1. Review and refine the previous title.
2. Ensure it’s within the 50-60 character range and optimized for SEO and engagement.
3. Finalize the title in the correct language.

# Examples:

**Example 1:**
- **Input:** "Le Délicieux", ["French gastronomy"], "Lyon", "Le Délicieux - French gastronomy in Lyon"
- **Output:** "Le Délicieux - Gourmet French Cuisine in Lyon"

**Example 2:**
- **Input:** "Le Gourmet", ["Italian cuisine"], "Paris 9", "Le Gourmet - Italian Cuisine in Paris 9"
- **Output:** "Le Gourmet - Authentic Italian Cuisine in Paris 9"
    """,
    "head_meta_description_optimization": """
Enhance the previously generated meta description for your restaurant's website using the provided keywords, address, and the previous AI-generated description. The goal is to improve SEO, engagement, and clarity while maintaining consistency.

# Rules:
1. **Use the Previous Description**: Start with the previously generated description and refine it.
2. **Adjust Length**: Ensure the description is between **140-160 characters**. Modify wording if necessary.
3. **SEO & Engagement**: Ensure the description includes strategic keywords, the restaurant's address (if possible), and a call-to-action in a natural, compelling way.
4. **Unique Selling Points**: Highlight the restaurant's key features or specialties.
5. **No Redundancy**: Ensure there is no repetition from previous generations, maintaining consistency in messaging.

# Steps:
1. Review and refine the previous meta description.
2. Ensure it’s within the 140-160 character range and optimized for SEO and engagement.
3. Finalize the description in the correct language.

# Examples:

**Example 1:**
- **Input:** "Le Délicieux", ["French gastronomy"], "Lyon", "Le Délicieux offers gourmet French cuisine in Lyon. Book your table today!"
- **Output:** "Le Délicieux - Enjoy gourmet French cuisine in Lyon. Visit us for an unforgettable dining experience. Book your table now!"

**Example 2:**
- **Input:** "Le Gourmet", ["Italian cuisine"], "Paris 9", "Le Gourmet serves delicious Italian cuisine in the heart of Paris 9. Join us for a memorable meal!"
- **Output:** "Le Gourmet - Savor authentic Italian cuisine in Paris 9. Join us for a delightful meal. Book your table today!"
""",
    "gallery_block_title_optimization": """
Enhance the previously generated gallery title for your restaurant's website using the provided restaurant name, keywords, and the previous AI-generated title. The goal is to improve SEO, engagement, and clarity while maintaining consistency.

# Rules:
1. **Use the Previous Title**: Start with the previously generated gallery title and refine it.
2. **Adjust Length**: Ensure the title is under **60 characters**. Modify wording if necessary.
3. **SEO & Engagement**: Ensure the title incorporates relevant keywords, reflects the restaurant’s values, and is engaging and inviting.
4. **Avoid Redundancy**: Avoid repeating words or phrases from the original title or input, ensuring freshness.
5. **Specificity**: Focus on specific elements like ingredients, preparation, or values (e.g., organic, local). Avoid vague terms like “authentic” or “unique”.

# Steps:
1. Review and refine the previous gallery title.
2. Ensure it fits within the 60-character limit and optimizes for SEO and engagement.
3. Finalize the title in the correct language.

# Examples:

**Example 1:**
- **Input:** "Le Délicieux", ["French gastronomy", "locally sourced ingredients"], "Le Délicieux showcases fresh, locally sourced French gastronomy."
- **Output:** "Le Délicieux - Discover Fresh, Locally Sourced French Cuisine"

**Example 2:**
- **Input:** "La Trattoria", ["Italian food", "handmade pasta"], "La Trattoria serves handmade pasta with locally sourced ingredients."
- **Output:** "La Trattoria - Enjoy Handmade Pasta with Fresh Local Ingredients"
""",
    "gallery_block_subtitle_optimization": """
Enhance the previously generated subtitle for your restaurant's website using the provided title, restaurant name, keywords, and the previous AI-generated subtitle. The goal is to improve SEO, engagement, and clarity while maintaining consistency.

# Rules:
1. **Use the Previous Subtitle**: Start with the previously generated subtitle and refine it.
2. **Adjust Length**: Ensure the subtitle is under **80 characters**. Modify wording if necessary.
3. **SEO & Engagement**: Ensure the subtitle complements the title by incorporating relevant keywords, reflecting the restaurant's values (e.g., organic, local), and being engaging.
4. **Avoid Redundancy**: Ensure the subtitle does not repeat words or phrases from the title, maintaining freshness and cohesion.
5. **Specificity**: Focus on specific elements like ingredients, preparation, or restaurant values. Avoid vague terms like "authentic" or "unique."

# Steps:
1. Review and refine the previous subtitle.
2. Ensure it fits within the 80-character limit and enhances the title.
3. Finalize the subtitle in the correct language.

# Examples:

**Example 1:**
- **Input:** "Le Délicieux", "Le Délicieux - Discover Fresh, Locally Sourced French Cuisine", ["French gastronomy", "locally sourced ingredients"]
- **Output:** "Savor seasonal dishes made with the finest local ingredients for an unforgettable dining experience."

**Example 2:**
- **Input:** "La Trattoria", "La Trattoria - Enjoy Handmade Pasta with Fresh Local Ingredients", ["Italian food", "handmade pasta"]
- **Output:** "Indulge in classic Italian flavors, from hand-rolled pasta to flavorful, fresh ingredients."
""",
    "description_block_title_optimization": """
    Optimize the description block title for the restaurant's website.

    # Instructions:
    - Review the current description block title and ensure it is informative, engaging, and SEO-friendly.
    - Ensure the title clearly reflects the focus of the content in the description block, whether it's about the restaurant's offerings, values, or location.
    - If the current title is too vague or unclear, revise it to better capture the essence of the content and engage the reader.

    # Output Format:
    - The optimized description block title should be concise, informative, and reflect the content of the block while being SEO-friendly.
    """,
    "description_block_subtitle_optimization": """
    Optimize the description block subtitle for the restaurant's website.

    # Instructions:
    - Review the current description block subtitle and ensure it supports the title and adds more context about the content.
    - Ensure it includes relevant SEO keywords and is inviting and engaging.
    - If the current subtitle is too generic or doesn't effectively complement the title, adjust the wording to improve clarity and engagement.

    # Output Format:
    - The optimized description block subtitle should be concise, SEO-friendly, and complement the description block title.
    """,
    "description_block_content_optimization": """
    Optimize the content for the description block on the restaurant's website.

    # Instructions:
    - Review the current description block content and ensure it is clear, informative, and SEO-friendly.
    - Ensure the content effectively showcases the restaurant's offerings, unique features, or values.
    - If the current content is too generic, lacks focus, or is repetitive, revise it to be more specific and engaging for the target audience.
    - Focus on integrating SEO keywords naturally while ensuring readability.

    # Output Format:
    - The optimized description block content should be clear, concise, and engaging, with a focus on improving SEO without compromising the user experience.
    """,
    "social_media_block_title_optimization": """
Enhance the previously generated title to introduce the social media posts section for your restaurant using the provided specialties, ambiance, tone, target audience, and the previous AI-generated title. The goal is to improve engagement, consistency, and brand alignment while maintaining the dynamic nature of social media interactions.

# Rules:
1. **Use the Previous Title**: Start with the previously generated title and refine it.
2. **Adjust for Brand Consistency**: Ensure the title aligns with your restaurant's specialties, ambiance, tone, and target audience.
3. **Social Media Appeal**: The title should be catchy and engaging, resonating with the kind of conversations your audience expects on social media.
4. **Avoid Repetition**: Ensure the title doesn’t repeat elements unnecessarily and remains fresh and relevant.
5. **Engagement**: Focus on making the title feel dynamic and in tune with the interactive nature of social media.

# Steps:
1. Review and refine the previous title to ensure it’s fresh, engaging, and aligned with your brand.
2. Ensure it resonates with your social media audience while staying consistent with the customer reviews section.
3. Finalize the title in the correct language and make sure it’s optimized for social media interactions.

# Examples:

**Example 1:**
- **Input:** "Le Délicieux", ["Viennoiseries, tartes aux fruits"], "Chaleureuse, artisanale", "Élégant et convivial", ["raffiné, authentique, fait maison"], "Amateurs de douceurs, touristes, familles"
- **Previous Title:** "Get Social: Dive Into French Pastries and Cozy Vibes!"
- **Output:** "Get Social: Explore Sweet French Pastries and Cozy Moments!"

**Example 2:**
- **Input:** "El Fiesta", ["Tacos, enchiladas, margaritas"], "Festive, colorée", "Chaleureux et vibrant", ["fiesta, épices, fraîcheur"], "Jeunes adultes, groupes d’amis, foodies"
- **Previous Title:** "Join the Fiesta: Tacos and Margaritas on Social!"
- **Output:** "Join the Fiesta: Tacos, Margaritas, and All the Fun!"

**Example 3:**
- **Input:** "Ramen Zen", ["Ramen, udon, gyoza"], "Moderne, zen", "Minimaliste et raffiné", ["umami, traditionnel, savoureux"], "Millennials, amateurs de cuisine japonaise"
- **Previous Title:** "Noodles & Vibes: Share Your Ramen Moments!"
- **Output:** "Noodles & Vibes: Share Your Zen Ramen Moments with Us!"
""",
    "cta_block_title_optimization": """
Enhance the previously generated H2 title for your restaurant's website, positioned above the platform button links (e.g., UberEats, Deliveroo), using the provided brand style, tone, and the previous AI-generated title. The goal is to improve engagement, consistency, and brand alignment.

# Rules:
1. **Use the Previous Title**: Start with the previously generated title and refine it.
2. **Adjust for Brand Consistency**: Ensure the title reflects the brand’s style and tone accurately, and aligns with the platform links below.
3. **Core Message**: The title should convey a clear and compelling message that resonates with the action of ordering via platforms like UberEats or Deliveroo.
4. **Engagement**: The title should be catchy and concise, with 3 to 6 words, making it visually appealing and fitting the dynamic nature of online food ordering.
5. **Avoid Redundancy**: Avoid repeating words or phrases from the previous title or brand messaging unnecessarily.

# Steps:
1. Review and refine the previous title to ensure it’s compelling and aligned with the brand tone.
2. Ensure it’s visually appealing and resonates with the audience while complementing the platform buttons.
3. Finalize the title in the correct language, ensuring it maintains consistency with the overall brand identity.

# Examples:

**Example 1:**
- **Input:** "Modern, minimal", "Cool and confident", "EN", "Crave It. Click It. Eat."
- **Previous Title:** "Crave It. Click It. Eat."
- **Output:** "Order Your Favorite, Delivered Fast"

**Example 2:**
- **Input:** "Family-friendly, cute", "Sweet and joyful", "EN", "Big Flavor in Every Bite"
- **Previous Title:** "Big Flavor in Every Bite"
- **Output:** "Satisfy Your Cravings Now!"

**Example 3:**
- **Input:** "Local, authentique", "Chaleureux et traditionnel", "FR", "Les Saveurs local à domicile"
- **Previous Title:** "Les Saveurs local à domicile"
- **Output:** "Commandez les Saveurs du Terroir"
""",
    "reviews_block_title_optimization": """
Enhance the previously generated headline to introduce the customer reviews section on your restaurant's website using the provided restaurant name, specialties, ambiance, tone, target audience, and the previous AI-generated headline. The goal is to improve SEO, engagement, and clarity while maintaining consistency with the restaurant's brand.

# Rules:
1. **Use the Previous Headline**: Start with the previously generated headline and refine it.
2. **Adjust for Brand Consistency**: Ensure the headline aligns with the restaurant’s unique character, specialties, and brand tone.
3. **Emotional and Trust-Inspiring**: The headline should evoke positive emotions, trust, and curiosity, encouraging visitors to explore customer reviews.
4. **Avoid Overused Terms**: Don’t use generic terms like “testimonials” or “customer feedback.” Instead, use creative and engaging expressions that align with the restaurant's vibe.
5. **Tone and Audience Fit**: Make sure the tone resonates with the target audience (e.g., playful, elegant, bold) and the ambiance of the restaurant.
6. **SEO Optimization**: Include keywords that reflect the restaurant’s values, food, and experience for better SEO impact.

# Steps:
1. Review and refine the previous headline to ensure it’s engaging, emotional, and consistent with the brand.
2. Ensure it aligns with the target audience and fits within the context of the restaurant’s overall tone and ambiance.
3. Finalize the headline in the correct language, ensuring it’s inviting and prompts curiosity about the customer reviews section.

# Examples:

**Example 1:**
- **Input:** "Le Délicieux", ["Viennoiseries, tartes aux fruits"], "Chaleureuse, artisanale", "Élégant et convivial", ["raffiné, authentique, fait maison"], "Amateurs de douceurs, touristes, familles"
- **Previous Headline:** "Mots Sucrés : Ce que disent nos gourmands"
- **Output:** "Mots Sucrés: Découvrez les avis de nos gourmands"

**Example 2:**
- **Input:** "El Fiesta", ["Tacos, enchiladas, margaritas"], "Festive, colorée", "Chaleureux et vibrant", ["fiesta, épices, fraîcheur"], "Jeunes adultes, groupes d’amis, foodies"
- **Previous Headline:** "Spice & Stories: Guests Share Their Fiesta Moments"
- **Output:** "Spice & Stories: Ce que nos invités disent de leur fiesta"

**Example 3:**
- **Input:** "Ramen Zen", ["Ramen, udon, gyoza"], "Moderne, zen", "Minimaliste et raffiné", ["umami, traditionnel, savoureux"], "Millennials, amateurs de cuisine japonaise"
- **Previous Headline:** "Noodle Narratives: Guests Share Their Slurpy Stories"
- **Output:** "Noodle Narratives: Découvrez ce que nos invités disent de nos saveurs"
""",
}
DEPENDENCIES = {
    "restaurant_page_url_generation": [
        "address",
        "keywords",
        "language",
    ],
    "h1_title_generation": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "restaurantContext",
    ],
    "head_meta_description_generation": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "context",
        "inspiration",
        "brandTone",
    ],
    "head_meta_twitter_description_generation": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "inspiration",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "restaurantContext",
        "specificsDirectives",
    ],
    "gallery_block_title_generation": [
        "restaurantName",
        "keywords",
        "context",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
    ],
    "gallery_block_subtitle_generation": [
        "restaurantName",
        "keywords",
        "context",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "specificsDirectives",
    ],
    "description_block_generation": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "inspiration",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "restaurantContext",
        "specificsDirectives",
    ],
    "organization_keywords_generation": ["organizationName", "keywords"],
    "reviews_block_title_generation": [
        "organizationName",
        "keywords",
        "context",
        "language",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
    ],
    "social_media_block_title_generation": [
        "organizationName",
        "keywords",
        "targetAudience",
        "brandTone",
        "restaurantOffers",
        "restaurantContext",
        "context",
        "language",
    ],
    "cta_block_title_generation": [
        "organizationName",
        "keywords",
        "targetAudience",
        "brandTone",
        "restaurantOffers",
        "restaurantContext",
        "language",
    ],
    "h1_title_optimization": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "restaurantContext",
        "previousGeneration",
        "previousGeneration",
    ],
    "head_meta_description_optimization": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "context",
        "inspiration",
        "brandTone",
        "previousGeneration",
    ],
    "gallery_block_title_optimization": [
        "restaurantName",
        "keywords",
        "context",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "previousGeneration",
    ],
    "gallery_block_subtitle_optimization": [
        "restaurantName",
        "keywords",
        "context",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "specificsDirectives",
        "previousGeneration",
    ],
    "description_block_title_optimization": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "inspiration",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "restaurantContext",
        "specificsDirectives",
        "previousGeneration",
    ],
    "description_block_subtitle_optimization": [
        "restaurantName",
        "keywords",
        "context",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "specificsDirectives",
        "previousGeneration",
    ],
    "description_block_content_optimization": [
        "restaurantName",
        "keywords",
        "address",
        "language",
        "inspiration",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
        "restaurantContext",
        "specificsDirectives",
        "previousGeneration",
    ],
    "social_media_block_title_optimization": [
        "organizationName",
        "previousGeneration",
        "keywords",
        "targetAudience",
        "brandTone",
        "restaurantOffers",
        "restaurantContext",
        "context",
        "language",
    ],
    "cta_block_title_optimization": [
        "organizationName",
        "previousGeneration",
        "keywords",
        "targetAudience",
        "brandTone",
        "restaurantOffers",
        "restaurantContext",
        "language",
    ],
    "reviews_block_title_optimization": [
        "previousGeneration",
        "organizationName",
        "keywords",
        "context",
        "language",
        "brandTone",
        "targetAudience",
        "restaurantOffers",
    ],
    "map_block_title_generation": [
        "organizationName",
        "language",
    ],
    "map_description_generation": [
        "organizationName",
        "language",
        "context",
        "inspiration",
        "brandTone",
    ],
    "map_twitter_description_generation": [
        "organizationName",
        "language",
        "context",
        "inspiration",
        "brandTone",
    ],
    "map_keywords_generation": [
        "organizationName",
        "location",
        "restaurantType",
        "keywords",
    ],
}
MIN_LENGTH = {
    "restaurant_page_url_generation": 1,
    "h1_title_generation": 50,
    "head_meta_description_generation": 140,
    "head_meta_twitter_description_generation": 120,
    "gallery_block_title_generation": 1,
    "gallery_block_subtitle_generation": 1,
    "organization_keywords_generation": 1,
    "reviews_block_title_generation": 1,
    "social_media_block_title_generation": 1,
    "cta_block_title_generation": 1,
    "map_block_title_generation": 1,
    "map_description_generation": 140,
    "map_twitter_description_generation": 120,
    "map_keywords_generation": 1,
    "h1_title_optimization": 50,
    "head_meta_description_optimization": 140,
    "gallery_block_title_optimization": 1,
    "gallery_block_subtitle_optimization": 1,
    "description_block_title_optimization": 1,
    "description_block_subtitle_optimization": 1,
    "description_block_content_optimization": 100,
    "social_media_block_title_optimization": 1,
    "cta_block_title_optimization": 1,
    "reviews_block_title_optimization": 1,
}
MAX_LENGTH = {
    "restaurant_page_url_generation": 120,
    "h1_title_generation": 60,
    "head_meta_description_generation": 160,
    "head_meta_twitter_description_generation": 160,
    "gallery_block_title_generation": 60,
    "gallery_block_subtitle_generation": 80,
    "organization_keywords_generation": 50,
    "reviews_block_title_generation": 60,
    "social_media_block_title_generation": 60,
    "cta_block_title_generation": 60,
    "map_block_title_generation": 60,
    "map_description_generation": 160,
    "map_twitter_description_generation": 160,
    "map_keywords_generation": 50,
    "h1_title_optimization": 60,
    "head_meta_description_optimization": 160,
    "gallery_block_title_optimization": 60,
    "gallery_block_subtitle_optimization": 80,
    "description_block_title_optimization": 100,
    "description_block_subtitle_optimization": 130,
    "description_block_content_optimization": 160,
    "social_media_block_title_optimization": 60,
    "cta_block_title_optimization": 60,
    "reviews_block_title_optimization": 60,
}
LANGUAGES = {"FR": "French", "EN": "English", "ES": "Spanish", "IT": "Italian"}


class Address(BaseModel):
    locality: str
    postalCode: str


class TextGenerationPayload(BaseModel):
    type: str
    language: str
    restaurantName: Optional[str] = None
    organizationName: Optional[str] = None
    address: Optional[Address] = None
    keywords: Optional[List[str]] = None
    brandTone: Optional[List[str]] = None
    targetAudience: Optional[List[str]] = None
    specificsDirectives: Optional[List[str]] = None
    restaurantOffers: Optional[List[str]] = None
    restaurantContext: Optional[List[str]] = None
    context: Optional[List[Dict[str, Any]]] = None
    inspiration: Optional[List[str]] = None
    previousGeneration: Optional[str] = None


class Section(BaseModel):
    subtitle: str
    text: str


class Block(BaseModel):
    title: str
    sections: List[Section]


class URLGeneratedModel(BaseModel):
    specialty: str
    location: str


class BlockGenerated(BaseModel):
    blocks: List[Block]

    @model_validator(mode="after")
    def check_blocks(self):
        if len(self.blocks) != 2:
            raise ValueError("Two blocks are required.")
        if len(self.blocks[0].sections) != 2:
            raise ValueError("The first block must contain exactly two sections.")
        if len(self.blocks[1].sections) != 2:
            raise ValueError("The second block must contain exactly two sections.")
        return self


class TextGeneratedModel(BaseModel):
    @classmethod
    def create_with_constraints(cls, type_name: str):
        """Factory method to create a model with type-specific constraints."""
        min_len = MIN_LENGTH.get(type_name, 1)
        max_len = MAX_LENGTH.get(type_name, 300)

        class TextGenerated(BaseModel):
            text: str = Field(min_length=min_len, max_length=max_len)

        return TextGenerated


class KeywordsGenerated(BaseModel):
    keywords: List[str] = Field(min_length=3, max_length=3)

    @model_validator(mode="after")
    def check_keywords(self):
        if not self.keywords or not isinstance(self.keywords, list):
            raise ValueError("keywords must be a non-empty list")
        for keyword in self.keywords:
            if not isinstance(keyword, str):
                raise ValueError("Each keyword must be a string")
        if len(self.keywords) != 3:
            raise ValueError("Exactly three keywords are required")
        return self


class AiConfig(BaseModel):
    frequencyPenalty: Optional[float] = 0
    temperature: Optional[float] = 1.0
    maxTokens: Optional[float] = 4000
    timeout: Optional[float] = 55
    model: Optional[str] = "gpt-4o-mini"
    topP: Optional[float] = 1.0


class AiResponseMetadata(BaseModel):
    relatedEntityCollection: str
    type: str
    message: List[Dict[str, str]]
    completionText: str
    completionTokenCount: int
    promptTokenCount: int
    completionTimeInMilliseconds: int
    modelConfig: Optional[AiConfig] = None
    numberOfRetries: Optional[int] = 0


class TextGeneration(BaseModel):
    aiResponse: Any
    aiInteractionDetails: AiResponseMetadata


class TooManyRetriesError(Exception):
    """Raised when the number of retries exceeds the allowed limit."""

    pass
