import { z } from 'zod';

import { objectIdValidator } from '../utils/validators';

export const getStoreLocatorStoresParamsValidator = z.object({
    organizationId: objectIdValidator,
});

export type GetStoreLocatorStoresParamsDto = z.infer<typeof getStoreLocatorStoresParamsValidator>;

// -------------------------------------------------------------------------------

export const getOrganizationConfigurationParamsValidator = z.object({
    organizationId: objectIdValidator,
});

export type GetOrganizationConfigurationParamsDto = z.infer<typeof getOrganizationConfigurationParamsValidator>;

// -------------------------------------------------------------------------------

export const publishStoreLocatorParamsValidator = z.object({
    organizationId: objectIdValidator,
});

export type PublishStoreLocatorParamsDto = z.infer<typeof publishStoreLocatorParamsValidator>;
