import { nativeEnum, z } from 'zod';

import {
    isValidLanguageCodeISO_1,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorLanguage,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

import { objectIdValidator, urlValidator } from '../utils';
import { updateStoreLocatorParamsValidator } from './shared';

//-----------------------------------------------------------------

export const getStoreLocatorOrganizationConfigurationValidator = z.object({
    cloudfrontDistributionId: z.string(),
    organizationName: z.string(),
    baseUrl: z.string(),
    tailwindConfig: z.string(),
    tailwindClassesMap: z.string(),
    favIconUrl: urlValidator(),
    s3BucketName: z.string(),
});

export type GetStoreLocatorOrganizationConfigurationDto = z.infer<typeof getStoreLocatorOrganizationConfigurationValidator>;

// -------------------------------------------------------------------------------

export const updateOrganizationConfigurationParamsValidator = updateStoreLocatorParamsValidator;

export type UpdateOrganizationConfigurationParamsDto = z.infer<typeof updateOrganizationConfigurationParamsValidator>;

// -------------------------------------------------------------------------------

export const updateOrganizationConfigurationAiSettingsBodyValidator = z.object({
    aiSettings: z.object({
        tone: z.array(z.string()).optional(),
        languageStyle: z.nativeEnum(StoreLocatorAiSettingsLanguageStyle).optional(),
        attributeIds: z.array(z.string()).optional(),
        restaurantKeywordIds: z.array(objectIdValidator).optional(),
        specialAttributes: z
            .array(
                z.object({
                    restaurantId: objectIdValidator,
                    text: z.string(),
                })
            )
            .optional(),
    }),
});

export type UpdateOrganizationConfigurationAiSettingsBodyDto = z.infer<typeof updateOrganizationConfigurationAiSettingsBodyValidator>;

// -------------------------------------------------------------------------------

export const updateRestaurantPagesParamsValidator = updateStoreLocatorParamsValidator;

export type UpdateRestaurantPagesParamsDto = z.infer<typeof updateRestaurantPagesParamsValidator>;

// --------------------------------------------------------------------------------

export const updateOrganizationConfigurationStorePagesBodyValidator = z.object({
    data: z.record(z.nativeEnum(StoreLocatorRestaurantPageElementIds), z.array(z.string())),
});

export type UpdateOrganizationConfigurationStorePagesBodyDto = z.infer<typeof updateOrganizationConfigurationStorePagesBodyValidator>;

// -------------------------------------------------------------------------------

export const updateOrganizationConfigurationLanguagesBodyValidator = z.object({
    languages: z.object({
        primary: nativeEnum(StoreLocatorLanguage).refine(isValidLanguageCodeISO_1, {
            message: 'Primary language code must be a valid ISO 639-1 code defined in LanguageCodeISO_1 enum',
        }),
        secondary: z.array(
            z.nativeEnum(StoreLocatorLanguage).refine(isValidLanguageCodeISO_1, {
                message: 'Secondary language code must be a valid ISO 639-1 code defined in LanguageCodeISO_1 enum',
            })
        ),
    }),
});

export type UpdateOrganizationConfigurationLanguagesBodyDto = z.infer<typeof updateOrganizationConfigurationLanguagesBodyValidator>;

// -------------------------------------------------------------------------------

export const checkForStoreLocatorRestaurantPagesParamsValidator = updateStoreLocatorParamsValidator;

export type CheckForStoreLocatorRestaurantPagesParamsDto = z.infer<typeof checkForStoreLocatorRestaurantPagesParamsValidator>;

// -------------------------------------------------------------------------------

export const checkForStoreLocatorRestaurantPagesResponseValidator = z.object({
    organizationId: objectIdValidator,
    hasMissingRestaurantPages: z.boolean(),
    hasUpdatedPages: z.boolean(),
    hasAtLeastOnePageGenerated: z.boolean(),
});

export type CheckForStoreLocatorRestaurantPagesResponseDto = z.infer<typeof checkForStoreLocatorRestaurantPagesResponseValidator>;

// -------------------------------------------------------------------------------
