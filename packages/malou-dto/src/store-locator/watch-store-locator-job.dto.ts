import { z } from 'zod';

import { StoreLocatorJobType, WatcherStatus } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils/validators';

export const watchStoreLocatorJobParamsValidator = z.object({
    organizationId: objectIdValidator,
    jobId: objectIdValidator,
});

export interface WatchStoreLocatorJobResponseDto {
    status: WatcherStatus;
    type?: StoreLocatorJobType;
    error?: string;
}

// ----------------------------------------------------------------

export const watchStoreLocatorPagesGenerationParamsValidator = watchStoreLocatorJobParamsValidator;
export type WatchStoreLocatorPagesGenerationParamsDto = z.infer<typeof watchStoreLocatorPagesGenerationParamsValidator>;

// ----------------------------------------------------------------

export const watchStoreLocatorStorePublicationParamsValidator = z.object({
    organizationId: objectIdValidator,
    jobId: objectIdValidator,
});
export type WatchStoreLocatorStorePublicationParamsDto = z.infer<typeof watchStoreLocatorStorePublicationParamsValidator>;

// ----------------------------------------------------------------

export const getStoreLocatorOrganizationJobsParamsValidator = z.object({
    organizationId: objectIdValidator,
});
export type GetStoreLocatorOrganizationJobsParamsDto = z.infer<typeof getStoreLocatorOrganizationJobsParamsValidator>;

export interface GetStoreLocatorOrganizationJobResponseDto {
    jobId: string;
    jobType: StoreLocatorJobType;
    jobStartDate?: Date;
}
