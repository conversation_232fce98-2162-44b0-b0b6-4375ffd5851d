import { z } from 'zod';

import { latlngDtoValidator } from '../../restaurant';
import { objectIdValidator } from '../../utils';
import { ctaToDisplayValidator, imageValidator } from '../shared';

export const storeLocatorMapStoreValidator = z.object({
    id: objectIdValidator,
    restaurantName: z.string(),
    relativePath: z.string(),
    fullAddress: z.string(),
    coordinates: latlngDtoValidator,
    phone: z.string().optional(),
    itineraryUrl: z.string(),
    isNotOpenedYet: z.boolean(),
    ctas: z.array(ctaToDisplayValidator).optional(),
    image: imageValidator,
});
