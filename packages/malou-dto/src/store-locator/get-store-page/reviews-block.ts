import { z } from 'zod';

import { ctaToDisplayValidator } from '../shared';

const reviewValidator = z.object({
    picture: z.union([
        z.object({
            url: z.string(),
        }),
        z.object({
            initials: z.string(),
            color: z.string(),
        }),
    ]),
    userName: z.string(),
    publishedAt: z.string().optional(),
    starsCount: z.number(),
    platformKey: z.string(),
    content: z.string(),
});
export type GetStoreLocatorReviewDto = z.infer<typeof reviewValidator>;

export const storeLocatorStorePageReviewsBlockValidator = z.object({
    title: z.string(),
    reviews: z.array(reviewValidator),
    cta: ctaToDisplayValidator.optional(),
});

export const storeLocatorStorePageDraftReviewsBlockValidator = z.object({
    title: z.string(),
    reviews: z.array(reviewValidator).optional(),
    cta: ctaToDisplayValidator.optional(),
});
