import { z } from 'zod';

import { urlValidator } from '../../utils';

const storeLocatorStorePageInstagramBlockValidator = z.object({
    socialAccount: z.object({
        name: z.string(),
        url: urlValidator(),
        logoUrl: urlValidator(),
        followersCount: z.string(),
        publicationsCount: z.string(),
    }),
    publications: z.array(
        z.object({
            imageUrl: urlValidator(),
            imageDescription: z.string(),
            mediaType: z.string(),
            url: urlValidator(),
            likesCount: z.string().optional(),
            caption: z.string(),
            mediaCount: z.number(),
            isFirstMediaVideo: z.boolean(),
        })
    ),
});

export const storeLocatorStorePageSocialNetworksBlockValidator = z.object({
    title: z.string(),
    socialNetworks: z.object({
        instagram: storeLocatorStorePageInstagramBlockValidator.optional(),
    }),
});

export const storeLocatorStorePageDraftSocialNetworksBlockValidator = z.object({
    title: z.string(),
    socialNetworks: z.object({
        instagram: storeLocatorStorePageInstagramBlockValidator.nullable().optional(),
    }),
});

export type GetStoreLocatorStorePageInstagramBlockDto = z.infer<typeof storeLocatorStorePageInstagramBlockValidator>;
