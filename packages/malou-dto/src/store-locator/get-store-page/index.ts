import { z } from 'zod';

import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { objectIdValidator, urlValidator } from '../../utils';
import { GetStoreLocatorHeadBlockDto, storeLocatorHeadBlockValidator } from '../common';
import { storeLocatorStorePageCallToActionsBlockValidator } from './call-to-actions-block';
import { storeLocatorStorePageDescriptionsBlockValidator } from './descriptions-block';
import { storeLocatorStorePageGalleryBlockValidator } from './gallery-block';
import { storeLocatorStorePageInformationBlockValidator } from './information-block';
import {
    GetStoreLocatorReviewDto,
    storeLocatorStorePageDraftReviewsBlockValidator,
    storeLocatorStorePageReviewsBlockValidator,
} from './reviews-block';
import {
    GetStoreLocatorStorePageInstagramBlockDto,
    storeLocatorStorePageDraftSocialNetworksBlockValidator,
    storeLocatorStorePageSocialNetworksBlockValidator,
} from './social-networks-block';

export const storeLocatorStoreValidator = z.object({
    id: objectIdValidator,
    name: z.string(),
    internalName: z.string().nullish().optional(),
    relativePath: z.string(),
    organizationName: z.string(),
    lang: z.nativeEnum(StoreLocatorLanguage),
    shouldDisplayWhiteMark: z.boolean(),
    headBlock: storeLocatorHeadBlockValidator,
    galleryBlock: storeLocatorStorePageGalleryBlockValidator,
    reviewsBlock: storeLocatorStorePageReviewsBlockValidator.optional(),
    callToActionsBlock: storeLocatorStorePageCallToActionsBlockValidator,
    socialNetworksBlock: storeLocatorStorePageSocialNetworksBlockValidator.optional(),
    descriptionsBlock: storeLocatorStorePageDescriptionsBlockValidator,
    informationBlock: storeLocatorStorePageInformationBlockValidator,
    styles: z.record(
        z.string(), // z.nativeEnum(StoreLocatorRestaurantPageElementIds),
        z.array(z.string()) // Values are arrays of strings
    ),
});

export const storeLocatorDraftStoreValidator = storeLocatorStoreValidator.extend({
    reviewsBlock: storeLocatorStorePageDraftReviewsBlockValidator.optional(),
    socialNetworksBlock: storeLocatorStorePageDraftSocialNetworksBlockValidator,
    suggestionsForEdit: z.object({
        callToActions: z.record(z.string(), urlValidator()),
    }),
});

export type GetStoreLocatorStorePageDto = z.infer<typeof storeLocatorStoreValidator>;
export type GetStoreLocatorDraftStoreDto = z.infer<typeof storeLocatorDraftStoreValidator>;

export {
    storeLocatorHeadBlockValidator,
    storeLocatorStorePageCallToActionsBlockValidator,
    storeLocatorStorePageDescriptionsBlockValidator,
    storeLocatorStorePageDraftReviewsBlockValidator,
    storeLocatorStorePageDraftSocialNetworksBlockValidator,
    storeLocatorStorePageGalleryBlockValidator,
    storeLocatorStorePageInformationBlockValidator,
    storeLocatorStorePageReviewsBlockValidator,
    storeLocatorStorePageSocialNetworksBlockValidator,
};
export type { GetStoreLocatorHeadBlockDto, GetStoreLocatorReviewDto, GetStoreLocatorStorePageInstagramBlockDto };
