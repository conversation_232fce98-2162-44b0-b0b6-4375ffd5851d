import { PostedStatus } from '@malou-io/package-utils';

import { KeywordAnalysisDto } from '../keywords';

type ContentDto = {
    from: {
        name: string;
        email: string;
    };
    object: string;
    messageHTML: string;
};

export type CommentReviewDto = {
    _id: string;
    aiInteractionIdUsed?: string;
    author?: {
        _id?: string;
        name?: string;
        picture?: string;
    };
    content?: ContentDto;
    id: string;
    isMalou: boolean;
    isRepliedFromAggregatedView?: boolean;
    keywordAnalysis?: KeywordAnalysisDto;
    posted?: PostedStatus;
    retries?: number;
    socialId?: string;
    socialTranslatedText?: string;
    socialUpdatedAt?: string;
    templateIdUsed?: string;
    text: string;
    user?: {
        displayName?: string;
        socialId: string;
    };
};

export type SocialAttachmentReviewDto = {
    urls: UrlsSocialAttachmentReviewDto;
    type: string;
};

export type UrlsSocialAttachmentReviewDto = {
    original: string;
    small?: string;
};

export type ResponseStyleDto = {
    style: string;
    toneOfVoice: string;
    responseStructure: string;
};

export type ReviewerNameValidationDto = {
    gender: string;
    firstName: string;
    isFirstNameValid: boolean;
    lastName: string;
    isLastNameValid: boolean;
};
