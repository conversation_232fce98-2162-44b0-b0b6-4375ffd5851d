import { Civility, PlatformKey, SemanticAnalysisFetchStatus } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const privateReviewJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'PrivateReview',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        text: {
            type: 'string',
        },
        lang: {
            type: 'string',
        },
        key: {
            type: 'string',
            default: PlatformKey.PRIVATE,
        },
        campaignId: {
            type: 'string',
            format: 'objectId',
            ref: 'Campaign',
        },
        socialCreatedAt: {
            type: 'string',
            format: 'date-time',
            default: Date.now,
            description: 'socialSortDate is computed from this field and should be always updated if this field changes',
        },
        socialSortDate: {
            type: 'string',
            format: 'date-time',
            default: Date.now,
            description:
                'This field exists to sort private reviews in the same way as public reviews. It is computed from socialCreatedAt and should be always updated if socialCreatedAt changes',
        },
        rating: {
            type: 'integer',
            default: null,
        },
        clientId: {
            type: 'string',
            format: 'objectId',
            ref: 'Client',
        },
        archived: {
            type: 'boolean',
            default: false,
        },
        comments: {
            type: 'array',
            items: {
                $ref: '#/definitions/Comment',
            },
            default: [],
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        scanId: {
            type: 'string',
            format: 'objectId',
        },
        translationsId: {
            type: 'string',
            format: 'objectId',
        },
        semanticAnalysisFetchStatus: {
            description:
                'Indicates the status of the semantic analysis fetch : PENDING = message sent in the queue waiting to be treated, NO_RESULTS = empty array received from lambda, DONE = semantic analysis done, FAILED = error while fetching the semantic analysis',
            enum: [...Object.values(SemanticAnalysisFetchStatus), null],
        },
        responseStyle: {
            description: 'Response style used to generate the response to the review',
            anyOf: [
                {
                    type: 'null',
                },
                {
                    $ref: '#/definitions/ResponseStyle',
                },
            ],
            default: null,
        },
        matchedReviewsIds: {
            description: 'List of review ids that were used to generate the response to the review.',
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
            },
        },
        reviewerNameValidation: {
            description: 'Validation results for the reviewer name fields.',
            anyOf: [
                {
                    type: 'null',
                },
                {
                    $ref: '#/definitions/ReviewerNameValidation',
                },
            ],
            default: null,
        },
    },
    required: [
        '_id',
        'archived',
        'comments',
        'createdAt',
        'key',
        'rating',
        'restaurantId',
        'socialCreatedAt',
        'socialSortDate',
        'text',
        'updatedAt',
    ],
    definitions: {
        Comment: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                text: {
                    type: 'string',
                },
                socialUpdatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
                author: {
                    $ref: '#/definitions/Author',
                },
                templateIdUsed: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'objectId',
                            default: null,
                        },
                        {
                            type: 'null',
                        },
                    ],
                },
                isRepliedFromAggregatedView: {
                    type: 'boolean',
                    default: 'false',
                },
                content: {
                    $ref: '#/definitions/Content',
                },
            },
            required: ['_id', 'author', 'content', 'socialUpdatedAt', 'text'],
            title: 'Comment',
        },
        Author: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                name: {
                    type: 'string',
                },
            },
            required: ['_id', 'name'],
            title: 'Author',
        },
        Content: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                from: {
                    $ref: '#/definitions/From',
                },
                object: {
                    type: 'string',
                },
                messageHTML: {
                    type: 'string',
                },
            },
            required: ['_id', 'from', 'messageHTML', 'object'],
            title: 'Content',
        },
        From: {
            type: 'object',
            additionalProperties: false,
            properties: {
                name: {
                    type: 'string',
                },
                email: {
                    type: 'string',
                },
            },
            required: ['email', 'name'],
            title: 'From',
        },
        ResponseStyle: {
            type: 'object',
            additionalProperties: false,
            properties: {
                style: {
                    type: 'string',
                    description: 'The style of the response, e.g., "formal", "friendly", etc.',
                },
                toneOfVoice: {
                    type: 'string',
                    description: 'The tone of voice used in the response, e.g., "professional", "casual", etc.',
                },
                responseStructure: {
                    type: 'string',
                    description: 'The structure of the response, e.g., "bullet points", "paragraph", etc.',
                },
            },
            required: ['style', 'toneOfVoice', 'responseStructure'],
            title: 'ResponseStyle',
        },
        ReviewerNameValidation: {
            type: 'object',
            additionalProperties: false,
            properties: {
                gender: {
                    enum: Object.values(Civility),
                },
                firstName: {
                    type: 'string',
                },
                isFirstNameValid: {
                    type: 'boolean',
                },
                lastName: {
                    type: 'string',
                },
                isLastNameValid: {
                    type: 'boolean',
                },
            },
            required: ['gender', 'firstName', 'isFirstNameValid', 'lastName', 'isLastNameValid'],
            title: 'ReviewerNameValidation',
        },
    },
} as const satisfies JSONSchemaExtraProps;
