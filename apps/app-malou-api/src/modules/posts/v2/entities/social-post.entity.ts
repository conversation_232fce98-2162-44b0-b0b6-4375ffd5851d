import { DateTime } from 'luxon';
import { v4 as uuidv4 } from 'uuid';

import { GetMediaForEditionResponseDto, SocialPostDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import {
    DeviceType,
    EntityConstructor,
    HashtagType,
    PlatformKey,
    PostErrorData,
    PostFeedbacks,
    PostLocation,
    PostPublicationStatus,
    PostType,
    PostUserTag,
    roundUpToTheNext15MinuteInterval,
    SocialPostCallToAction,
    TiktokPrivacyStatus,
} from '@malou-io/package-utils';

import { PostAuthor } from ':modules/posts/v2/entities/author.entity';

export type SocialPostMedia = GetMediaForEditionResponseDto;

export type SocialPostProps = EntityConstructor<SocialPost> & { id: string };

export interface PostHashtag {
    id: string;
    text: string;
    isCustomerInput: boolean;
    isMain: boolean;
    type: HashtagType;
}

export class SocialPost {
    id: string;
    title?: string;
    text: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    isPublishing: boolean;
    postType: PostType;
    plannedPublicationDate: Date;
    attachments: SocialPostMedia[];
    hashtags?: {
        selected: PostHashtag[];
        suggested: PostHashtag[];
    };
    location?: PostLocation;
    callToAction?: SocialPostCallToAction;
    feedbacks?: PostFeedbacks;
    error?: PostErrorData;
    socialLink?: string;
    socialCreatedAt?: Date;
    author?: PostAuthor;
    userTagsList: (PostUserTag[] | null)[];
    bindingId?: string;
    tiktokOptions: {
        privacyStatus: TiktokPrivacyStatus;
        interactionAbility: {
            comment: boolean;
            duet: boolean;
            stitch: boolean;
        };
        contentDisclosureSettings: {
            isActivated: boolean;
            yourBrand: boolean;
            brandedContent: boolean;
        };
        autoAddMusic: boolean;
    };
    reelThumbnailFromMedia?: SocialPostDto['reelThumbnailFromMedia'];
    reelThumbnailFromFrame?: SocialPostDto['reelThumbnailFromFrame'];
    instagramCollaboratorsUsernames?: string[];
    createdFromDeviceType?: DeviceType;

    constructor(data: SocialPostProps) {
        this.id = data.id;
        this.title = data.title;
        this.text = data.text;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.isPublishing = data.isPublishing;
        this.postType = data.postType;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.attachments = data.attachments;
        this.hashtags = data.hashtags;
        this.location = data.location;
        this.feedbacks = data.feedbacks;
        this.callToAction = data.callToAction;
        this.error = data.error;
        this.socialLink = data.socialLink;
        this.socialCreatedAt = data.socialCreatedAt;
        this.author = data.author;
        this.userTagsList = data.userTagsList;
        this.bindingId = data.bindingId;
        this.tiktokOptions = data.tiktokOptions;
        this.reelThumbnailFromMedia = data.reelThumbnailFromMedia;
        this.reelThumbnailFromFrame = data.reelThumbnailFromFrame;
        this.instagramCollaboratorsUsernames = data.instagramCollaboratorsUsernames;
        this.createdFromDeviceType = data.createdFromDeviceType;
    }

    toDto(): SocialPostDto {
        return {
            id: this.id,
            title: this.title,
            text: this.text ?? '',
            platformKeys: this.platformKeys,
            published: this.published,
            isPublishing: this.isPublishing,
            postType: this.postType,
            plannedPublicationDate: this.plannedPublicationDate?.toISOString(),
            attachments: this.attachments,
            hashtags: this.hashtags ?? { selected: [], suggested: [] },
            location: this.location
                ? {
                      id: this.location.id,
                      name: this.location.name,
                      link: this.location.link,
                      location: this.location.location
                          ? {
                                latitude: this.location.location.latitude,
                                longitude: this.location.location.longitude,
                                street: this.location.location.street,
                                zip: this.location.location.zip,
                                city: this.location.location.city,
                                country: this.location.location.country,
                            }
                          : undefined,
                  }
                : null,
            callToAction: this.callToAction
                ? {
                      actionType: this.callToAction.actionType,
                      url: this.callToAction.url,
                  }
                : undefined,
            feedbacks: this.feedbacks
                ? {
                      id: this.feedbacks.id,
                      isOpen: this.feedbacks.isOpen,
                      participants: this.feedbacks.participants,
                      feedbackMessages: this.feedbacks.feedbackMessages.map((message) => ({
                          id: message.id,
                          type: message.type,
                          visibility: message.visibility,
                          text: message.text,
                          author: message.author,
                          createdAt: message.createdAt.toISOString(),
                          updatedAt: message.updatedAt.toISOString(),
                          publishedAt: message.publishedAt?.toISOString(),
                          lastUpdatedAt: message.lastUpdatedAt?.toISOString(),
                      })),
                      createdAt: this.feedbacks.createdAt.toISOString(),
                      updatedAt: this.feedbacks.updatedAt.toISOString(),
                  }
                : null,
            error: this.error,
            socialLink: this.socialLink,
            socialCreatedAt: this.socialCreatedAt?.toISOString(),
            author: this.author?.toDto(),
            userTagsList: this.userTagsList,
            bindingId: this.bindingId,
            tiktokOptions: this.tiktokOptions,
            reelThumbnailFromMedia: this.reelThumbnailFromMedia,
            reelThumbnailFromFrame: this.reelThumbnailFromFrame,
            instagramCollaboratorsUsernames: this.instagramCollaboratorsUsernames,
            createdFromDeviceType: this.createdFromDeviceType,
        };
    }

    cloneWith(data: Partial<SocialPostProps>): SocialPost {
        return new SocialPost({
            id: data.id ?? this.id,
            title: data.title ?? this.title,
            text: data.text ?? this.text,
            platformKeys: data.platformKeys ?? this.platformKeys,
            published: data.published ?? this.published,
            isPublishing: false,
            postType: data.postType ?? this.postType,
            plannedPublicationDate: data.plannedPublicationDate ?? this.plannedPublicationDate,
            attachments: data.attachments ?? this.attachments,
            hashtags: data.hashtags ?? this.hashtags,
            location: data.location ?? this.location,
            callToAction: data.callToAction ?? this.callToAction,
            feedbacks: data.feedbacks ?? this.feedbacks,
            error: data.error ?? this.error,
            socialLink: data.socialLink ?? this.socialLink,
            socialCreatedAt: data.socialCreatedAt ?? this.socialCreatedAt,
            author: data.author ?? this.author,
            userTagsList: data.userTagsList ?? this.userTagsList,
            bindingId: data.bindingId ?? this.bindingId,
            tiktokOptions: data.tiktokOptions ?? this.tiktokOptions,
            reelThumbnailFromMedia: data.reelThumbnailFromMedia ?? this.reelThumbnailFromMedia,
            reelThumbnailFromFrame: data.reelThumbnailFromFrame ?? this.reelThumbnailFromFrame,
            instagramCollaboratorsUsernames: data.instagramCollaboratorsUsernames ?? this.instagramCollaboratorsUsernames,
            createdFromDeviceType: data.createdFromDeviceType ?? this.createdFromDeviceType,
        });
    }

    static createEmpty({
        platformKeys,
        author,
        location,
        date,
        isReel,
        createdFromDeviceType,
    }: {
        platformKeys: PlatformKey[];
        author: PostAuthor;
        location: PostLocation | undefined;
        date: Date | undefined;
        isReel: boolean;
        createdFromDeviceType?: DeviceType;
    }): SocialPost {
        const tomorrow = DateTime.now().plus({ days: 1 });
        const initialDate = date ? DateTime.fromJSDate(date) : tomorrow;
        const plannedPublicationDate = roundUpToTheNext15MinuteInterval(initialDate.toJSDate());

        return new SocialPost({
            id: newDbId().toString(),
            text: '',
            platformKeys,
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            postType: isReel ? PostType.REEL : PostType.IMAGE,
            plannedPublicationDate,
            attachments: [],
            author,
            location,
            userTagsList: [],
            bindingId: uuidv4(),
            tiktokOptions: {
                privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                interactionAbility: {
                    comment: false,
                    duet: false,
                    stitch: false,
                },
                contentDisclosureSettings: {
                    isActivated: false,
                    yourBrand: false,
                    brandedContent: false,
                },
                autoAddMusic: false,
            },
            instagramCollaboratorsUsernames: [],
            createdFromDeviceType,
        });
    }
}
