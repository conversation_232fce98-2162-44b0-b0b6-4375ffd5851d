import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import { DbId, EntityRepository, ID, ILightNfc, INfc, INfcWithRestaurant, NfcModel, toDbIds } from '@malou-io/package-models';
import { isNotNil, MalouErrorCode, PlatformKey, WHEEL_OF_FORTUNE_PLATFORM_KEY } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { NfcsFilters } from ':helpers/filters/nfcs-filters';
import { logger } from ':helpers/logger';
import { NfcWithRestaurant } from ':modules/nfc/entities/nfc-with-restaurant.entity';

import PlatformsRepository from '../platforms/platforms.repository';
import { Nfc as NfcEntity } from './entities/nfc.entity';

@singleton()
export default class NfcsRepository extends EntityRepository<INfc> {
    constructor(private _platformsRepository: PlatformsRepository) {
        super(NfcModel);
    }

    async searchNfcs(searchFilters: NfcsFilters): Promise<{ total: number; docs: NfcWithRestaurant[] }> {
        const searchQuery = searchFilters.buildQuery();

        const matchQueryStage =
            searchQuery.$and.length > 0
                ? {
                      $match: searchQuery,
                  }
                : undefined;

        const pipeline = [
            {
                $lookup: {
                    from: 'restaurants',
                    localField: 'restaurantId',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $addFields: {
                    restaurant: {
                        $arrayElemAt: ['$restaurant', 0],
                    },
                },
            },
            {
                $project: {
                    restaurant: {
                        _id: 1,
                        name: 1,
                    },
                    name: 1,
                    platformKey: 1,
                    restaurantId: 1,
                    chipName: 1,
                    active: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
            matchQueryStage,
            {
                $facet: {
                    metadata: [
                        {
                            $count: 'count',
                        },
                    ],
                    data: [
                        searchFilters.page && searchFilters.limit ? { $skip: (searchFilters.page - 1) * searchFilters.limit } : null,
                        searchFilters.limit ? { $limit: searchFilters.limit } : null,
                    ].filter(isNotNil),
                },
            },
        ].filter(isNotNil);

        type PipelineResult = [
            {
                metadata: { count: number }[];
                data: INfcWithRestaurant[];
            },
        ];

        const result: PipelineResult = (await this.aggregate(pipeline)) as unknown as PipelineResult;

        const ids: string[] = result[0].data.map((e) => e._id.toString());
        const nfcs = await this.find({
            filter: { _id: { $in: ids } },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'restaurant',
                        select: ['_id', 'name', 'logo', 'address', 'active', 'internalName', 'totemDisplayName', 'type', 'boosterPack'],
                    },
                ],
            },
        });

        return {
            total: result[0].metadata[0]?.count ?? 0,
            docs: nfcs.map((nfc) => new NfcWithRestaurant(nfc as INfcWithRestaurant)),
        };
    }

    async findNfcByChipname(chipName: string): Promise<NfcWithRestaurant> {
        const nfcDocument = await this.findOne({
            filter: { chipName },
            options: {
                populate: [
                    {
                        path: 'restaurant',
                        populate: [{ path: 'logoPopulated' }] as any,
                    },
                ],
                lean: true,
            },
        });

        if (!nfcDocument) {
            throw new MalouError(MalouErrorCode.NFC_NOT_FOUND, {
                message: `NFC not found with chipname`,
                metadata: {
                    chipName,
                },
            });
        }

        return this.toNfcWithRestaurantEntity(nfcDocument as INfcWithRestaurant);
    }

    async getNfcByIdsPopulated(ids: string[]): Promise<NfcWithRestaurant[]> {
        const nfcDocuments = await this.find({
            filter: { _id: { $in: toDbIds(ids) } },
            options: {
                populate: [
                    {
                        path: 'restaurant',
                        populate: [{ path: 'logoPopulated' }] as any,
                    },
                ],
                lean: true,
            },
        });
        return nfcDocuments.map((nfcDocument) => this.toNfcWithRestaurantEntity(nfcDocument as INfcWithRestaurant));
    }

    async getLightNfcByRestaurantIds(restaurantIds: string[]): Promise<ILightNfc[]> {
        return this.find({
            filter: {
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            projection: {
                _id: 1,
                name: 1,
                chipName: 1,
                restaurantId: 1,
                platformKey: 1,
                type: 1,
            },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'restaurant',
                        select: ['_id', 'name', 'internalName'],
                    },
                ],
                comment: 'getNfcByRestaurantIds',
                ReadPreference: ReadPreference.SECONDARY_PREFERRED,
            },
        });
    }

    async getNfcByIds(ids: DbId[]): Promise<NfcEntity[]> {
        return (await Promise.all(ids.map((id) => this._getNfcById(id)))).filter(isNotNil);
    }

    async getActiveNfcsByRestaurantIdCount(restaurantId: ID): Promise<number> {
        return this.countDocuments({
            filter: { restaurantId, active: true },
        });
    }

    deleteNfcById(nfcId: string): Promise<{ acknowledged: boolean; deletedCount: number }> {
        return this.deleteOne({ filter: { _id: nfcId } });
    }

    toNfcWithRestaurantEntity(document: INfcWithRestaurant): NfcWithRestaurant {
        return new NfcWithRestaurant(document);
    }

    private async _getNfcById(id: DbId): Promise<NfcEntity | undefined> {
        const nfc = await this.findOneOrFail({ filter: { _id: id }, options: { lean: true } });
        let platformKey = nfc.platformKey;

        if (!platformKey) {
            return;
        }

        const platformToRetrieve = [PlatformKey.WEBSITE, WHEEL_OF_FORTUNE_PLATFORM_KEY].includes(platformKey)
            ? PlatformKey.GMB
            : platformKey;
        let platform = await this._platformsRepository.findOne({
            filter: {
                restaurantId: nfc.restaurantId,
                key: platformToRetrieve,
            },
        });

        if (!platform) {
            // If the platform is not found, it may have been disconnected, we try to get the GMB platform by default
            platformKey = PlatformKey.GMB;
            platform = await this._platformsRepository.findOne({
                filter: {
                    restaurantId: nfc.restaurantId,
                    key: platformToRetrieve,
                },
            });

            if (!platform) {
                logger.error('[NFCS][GET_NFC_PLATFORM] Platform not found', { nfc });
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: 'Platform not found for nfc',
                    metadata: { nfcId: nfc._id },
                });
            }
        }

        return new NfcEntity({ ...nfc, id: nfc._id.toString(), platform, platformKey });
    }
}
