import { render } from '@react-email/render';
import { singleton } from 'tsyringe';

import { ClosedFeedbackMailTemplate, NewFeedbackMessageEmailTemplate, OpenedFeedbackMailTemplate } from '@malou-io/package-emails';
import { IPost, IUser } from '@malou-io/package-models';
import {
    getPlatformDefinition,
    Locale,
    mapApplicationLanguageToLocale,
    PictureSize,
    PostPublicationStatus,
    PostSource,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { MailOptions } from ':modules/mailing/email-sender.service';
import { Receiver } from ':modules/mailing/interface';
import { MediasRepository } from ':modules/media/medias.repository';
import { NotificationPost } from ':modules/notifications/entities/child-entities/notification-post.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { Translation } from ':services/translation.service';

@singleton()
export class FeedbackEmailUseCases {
    constructor(
        private readonly _translationService: Translation,
        private readonly _mediasRepository: MediasRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async getNewFeedbackMessageEmail(user: IUser, receiver: Receiver, post: IPost, messageText: string): Promise<MailOptions | null> {
        const lang = mapApplicationLanguageToLocale(receiver.defaultLanguage);
        const translatorFunctions = this._translationService.fromLang({ lang }).mailing.feedback.new_feedback_message;

        const restaurant = await this._restaurantsRepository.findOneOrFail({ filter: { _id: post.restaurantId }, options: { lean: true } });
        const restaurantName = restaurant.internalName ?? restaurant.name;
        const subject = translatorFunctions.subject({ restaurantName });

        const postLink = this._getLink(post);
        const notificationPost = await this._getNotificationPost(post);
        if (!notificationPost) {
            logger.error('[FEEDBACK_EMAIL_USE_CASES][getNewFeedbackMessageEmail] - Notification post not found');
            return null;
        }
        const postStatus = this._getPostStatusText(post.published, lang);
        const fullPlatformKeys = (post.key ? [post.key] : (post.keys ?? []))
            .map((key) => getPlatformDefinition(key)?.fullName)
            .filter(Boolean)
            .join(', ');

        const html = render(
            NewFeedbackMessageEmailTemplate({
                postLink,
                unsubscribeLink: this._getUnsubscribeLink(receiver),
                feedbackText: messageText,
                receiver: receiver.name,
                locale: lang,
                username: `${user.name} ${user.lastname}`,
                restaurantName,
                postStatus,
                plannedPublicationDate: notificationPost.plannedPublicationDate ?? notificationPost.postCreatedAt,
                fullPlatformKeys,
                postText: notificationPost.text,
                postImgUrl: notificationPost.attachments.igFit, // igFit of the picture or thumbnail if the post is a video
            })
        );

        return {
            lang,
            to: receiver.email,
            subject,
            html,
        };
    }

    async getClosedFeedbackEmail(user: IUser, receiver: Receiver, post: IPost): Promise<MailOptions | null> {
        const postLink = this._getLink(post);
        const lang = mapApplicationLanguageToLocale(receiver.defaultLanguage);
        const translatorFunctions = this._translationService.fromLang({ lang }).mailing.feedback.closed_feedback;

        const [restaurant, notificationPost] = await Promise.all([
            this._restaurantsRepository.findOneOrFail({ filter: { _id: post.restaurantId }, options: { lean: true } }),
            this._getNotificationPost(post),
        ]);
        if (!notificationPost) {
            logger.error('[FEEDBACK_EMAIL_USE_CASES][getClosedFeedbackEmail] - Notification post not found');
            return null;
        }
        const restaurantName = restaurant.internalName ?? restaurant.name;
        const subject = translatorFunctions.subject({ restaurantName });

        const postStatus = this._getPostStatusText(post.published, lang);
        const fullPlatformKeys = (post.key ? [post.key] : (post.keys ?? []))
            .map((key) => getPlatformDefinition(key)?.fullName)
            .filter(Boolean)
            .join(', ');

        const unsubscribeLink = this._getUnsubscribeLink(receiver);
        const html = render(
            ClosedFeedbackMailTemplate({
                locale: lang,
                postLink,
                username: `${user.name} ${user.lastname}`,
                receiver: receiver.name,
                unsubscribeLink,
                restaurantName,
                postStatus,
                plannedPublicationDate: notificationPost.plannedPublicationDate ?? notificationPost.postCreatedAt,
                fullPlatformKeys,
                postText: notificationPost.text,
                postImgUrl: notificationPost.attachments.igFit, // igFit of the picture or thumbnail if the post is a video
            })
        );

        return {
            lang,
            to: receiver.email,
            subject,
            html,
        };
    }

    async getOpenedFeedbackEmail(user: IUser, receiver: Receiver, post: IPost): Promise<MailOptions | null> {
        const postLink = this._getLink(post);
        const lang = mapApplicationLanguageToLocale(receiver.defaultLanguage);
        const translatorFunctions = this._translationService.fromLang({ lang }).mailing.feedback.opened_feedback;

        const [restaurant, notificationPost] = await Promise.all([
            this._restaurantsRepository.findOneOrFail({ filter: { _id: post.restaurantId }, options: { lean: true } }),
            this._getNotificationPost(post),
        ]);
        if (!notificationPost) {
            logger.error('[FEEDBACK_EMAIL_USE_CASES][getOpenedFeedbackEmail] - Notification post not found');
            return null;
        }
        const restaurantName = restaurant.internalName ?? restaurant.name;
        const subject = translatorFunctions.subject({ restaurantName });

        const postStatus = this._getPostStatusText(post.published, lang);
        const fullPlatformKeys = (post.key ? [post.key] : (post.keys ?? []))
            .map((key) => getPlatformDefinition(key)?.fullName)
            .filter(Boolean)
            .join(', ');

        const unsubscribeLink = this._getUnsubscribeLink(receiver);
        const html = render(
            OpenedFeedbackMailTemplate({
                locale: lang,
                postLink,
                username: `${user.name} ${user.lastname}`,
                receiver: receiver.name,
                unsubscribeLink,
                restaurantName,
                postStatus,
                plannedPublicationDate: notificationPost.plannedPublicationDate ?? notificationPost.postCreatedAt,
                fullPlatformKeys,
                postText: notificationPost.text,
                postImgUrl: notificationPost.attachments.igFit, // igFit of the picture or thumbnail if the post is a video
            })
        );

        return {
            lang,
            to: receiver.email,
            subject,
            html,
        };
    }

    private _getLink(post: IPost): string {
        const isSeoPost = post.source === PostSource.SEO;
        return isSeoPost
            ? `${Config.baseAppUrl}/restaurants/${post.restaurantId}/seo/posts/list?postId=${post._id}&openFeedback=true`
            : `${Config.baseAppUrl}/restaurants/${post.restaurantId}/social/socialposts?postId=${post._id}&openFeedback=true`;
    }

    private _getUnsubscribeLink(user: Receiver): string {
        return `${Config.baseApiUrl}/users/${user._id}/unsubscribe?field=receiveFeedbacks`;
    }

    private _getPostStatusText(published: PostPublicationStatus, lang: Locale): string {
        const translationFunctions = this._translationService.fromLang({ lang }).mailing.feedback.new_feedback_message.post_status;
        switch (published) {
            case PostPublicationStatus.PUBLISHED:
                return translationFunctions.published();
            case PostPublicationStatus.PENDING:
                return translationFunctions.scheduled();
            case PostPublicationStatus.DRAFT:
                return translationFunctions.draft();
            case PostPublicationStatus.ERROR:
                return translationFunctions.error();
            default:
                return '';
        }
    }

    private async _getNotificationPost(post: IPost): Promise<NotificationPost | null> {
        const firstMedia = post.attachments?.[0] ? await this._mediasRepository.findById(post.attachments[0].toString()) : null;
        const platform = post.platformId ? await this._platformsRepository.getPlatformById(post.platformId?.toString()) : null;

        if (!post.restaurantId || !post.plannedPublicationDate) {
            logger.error('[FEEDBACK_EMAIL_USE_CASES][_getNotificationPost] - Missing restaurantId or plannedPublicationDate on post', {
                postId: post._id.toString(),
                restaurantId: post.restaurantId?.toString(),
                plannedPublicationDate: post.plannedPublicationDate?.toString(),
            });
            return null;
        }
        const platformWithId = platform?.id
            ? {
                  id: platform.id,
                  key: platform.key ?? post.key,
                  name: platform.name ?? '',
                  socialLink: platform.socialLink,
              }
            : undefined;
        return new NotificationPost({
            id: post._id.toString(),
            source: post.source,
            errorMessage: post.errorData ?? '',
            plannedPublicationDate: post.plannedPublicationDate ?? undefined,
            restaurantId: post.restaurantId?.toString(), // TODO: should be mandatory
            postCreatedAt: post.createdAt,
            postUpdatedAt: post.updatedAt,
            attachments: {
                [PictureSize.SMALL]: firstMedia?.getMediaPictureUrl(PictureSize.SMALL) ?? '',
                [PictureSize.IG_FIT]: firstMedia?.getMediaPictureUrl(PictureSize.IG_FIT) ?? '',
            },
            platform: platformWithId,
            text: post.text ?? '',
            isStory: post.isStory,
        });
    }
}
