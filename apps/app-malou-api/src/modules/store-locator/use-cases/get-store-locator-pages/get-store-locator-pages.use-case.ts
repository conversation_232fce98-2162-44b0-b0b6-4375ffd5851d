import { singleton } from 'tsyringe';

import { GetStoreLocatorPagesDto } from '@malou-io/package-dto';

import { GetStoreLocatorMapService } from ':modules/store-locator/services/get-store-locator-map/get-store-locator-map.service';
import { GetStoreLocatorStoresService } from ':modules/store-locator/services/get-store-locator-stores/get-store-locator-stores.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class GetStoreLocatorPagesUseCase {
    constructor(
        private readonly _getStoreLocatorStoresService: GetStoreLocatorStoresService,
        private readonly _getStoreLocatorMapService: GetStoreLocatorMapService,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorPagesDto> {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        const [stores, mapPages] = await Promise.all([
            this._getStoreLocatorStoresService.execute(storeLocatorOrganizationConfig),
            this._getStoreLocatorMapService.execute(storeLocatorOrganizationConfig, {
                isForEdit: false,
            }),
        ]);
        const urls = this._getUrls({ stores, maps: mapPages ?? [] });

        return {
            restaurantsPages: stores,
            mapPages: mapPages ?? [],
            urls,
        };
    }

    private _getUrls({
        stores,
        maps,
    }: {
        stores: GetStoreLocatorPagesDto['restaurantsPages'];
        maps: GetStoreLocatorPagesDto['mapPages'];
    }): GetStoreLocatorPagesDto['urls'] {
        const urls = stores.reduce(
            (acc, store) => {
                acc[store.lang] = acc[store.lang] ?? {};
                acc[store.lang]![`store.${store.id}`] = store.relativePath;
                return acc;
            },
            {} as GetStoreLocatorPagesDto['urls']
        );

        maps.forEach((map) => {
            urls[map.lang] = urls[map.lang] ?? {};
            urls[map.lang]!.map = map.relativePath;
        });

        return urls;
    }
}
