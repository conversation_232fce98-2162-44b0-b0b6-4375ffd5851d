import { singleton } from 'tsyringe';

import { WatchStoreLocatorJobResponseDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { StoreLocatorJobStatus, WatcherStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';

@singleton()
export class WatchPagesGenerationUseCase {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    async execute({ organizationId, jobId }: { organizationId: string; jobId: string }): Promise<WatchStoreLocatorJobResponseDto> {
        const [job] = await this._agendaSingleton.jobs({
            _id: toDbId(jobId),
            name: AgendaJobName.STORE_LOCATOR_CONTENT_GENERATION,
        });

        if (!job) {
            logger.error('[STORE_LOCATOR] Generation job not found', { organizationId, jobId });
            return {
                status: WatcherStatus.FAILED,
                error: 'Store locator pages generation job not found',
            };
        }

        if (job.attrs.data?.status === StoreLocatorJobStatus.PENDING) {
            return {
                status: WatcherStatus.RUNNING,
            };
        }

        if (job.attrs.data?.status === StoreLocatorJobStatus.FAILED) {
            logger.error('[STORE_LOCATOR] Generation job failed', {
                organizationId,
                jobId: job.attrs._id,
                error: job.attrs.failReason,
            });

            return {
                status: WatcherStatus.FAILED,
                error: 'Store locator pages generation failed',
            };
        }

        logger.info('[STORE_LOCATOR] Generation job completed successfully', {
            organizationId,
            jobId: job.attrs._id,
        });

        return {
            status: WatcherStatus.FINISHED,
        };
    }
}
