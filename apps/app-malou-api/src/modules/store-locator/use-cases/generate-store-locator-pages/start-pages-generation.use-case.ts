import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { MalouErrorCode, StoreLocatorJobStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';

@singleton()
export class StartPagesGenerationUseCase {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    async execute(organizationId: string): Promise<string> {
        const jobs = await this._agendaSingleton.jobs({
            name: AgendaJobName.STORE_LOCATOR_CONTENT_GENERATION,
            'data.organizationId': organizationId,
            'data.status': StoreLocatorJobStatus.PENDING,
        });

        if (jobs?.length ?? 0 > 0) {
            throw new MalouError(MalouErrorCode.STORE_LOCATOR_GENERATION_ALREADY_RUNNING, {
                message: 'Store locator pages generation is already running',
                metadata: { organizationId },
            });
        }

        logger.info('[STORE_LOCATOR] [Content generation] Calling job to start generation', {
            organizationId,
        });
        const job = await this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_CONTENT_GENERATION, {
            organizationId,
            status: StoreLocatorJobStatus.PENDING,
        });
        assert(job.attrs?._id, 'Job ID is not defined');

        return job.attrs._id.toString();
    }
}
