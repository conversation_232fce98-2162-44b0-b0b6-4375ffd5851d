import { singleton } from 'tsyringe';

import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class DeployAllStoreLocatorsUseCase {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _agendaSingleton: AgendaSingleton
    ) {}

    async execute(): Promise<void> {
        const storeLocatorOrganizationConfigs = await this._storeLocatorOrganizationConfigRepository.find({
            filter: {},
            projection: { organizationId: 1 },
            options: { lean: true },
        });

        await Promise.all(
            storeLocatorOrganizationConfigs.map((config) =>
                this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_DEPLOYMENT, {
                    organizationId: config.organizationId.toString(),
                    status: StoreLocatorJobStatus.PENDING,
                })
            )
        );
    }
}
