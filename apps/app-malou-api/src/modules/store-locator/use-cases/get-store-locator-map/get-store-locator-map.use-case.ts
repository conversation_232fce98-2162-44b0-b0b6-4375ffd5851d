import { singleton } from 'tsyringe';

import { GetStoreLocatorMapDto } from '@malou-io/package-dto';

import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GetStoreLocatorMapService } from ':modules/store-locator/services/get-store-locator-map/get-store-locator-map.service';

@singleton()
export class GetStoreLocatorMapUseCase {
    constructor(private readonly _getStoreLocatorMapService: GetStoreLocatorMapService) {}

    async execute(
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration,
        options: {
            isForEdit?: boolean;
        } = {}
    ): Promise<GetStoreLocatorMapDto[] | null> {
        return await this._getStoreLocatorMapService.execute(storeLocatorOrganizationConfig, options);
    }
}
