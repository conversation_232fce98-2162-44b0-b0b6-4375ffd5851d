import { singleton } from 'tsyringe';

import { GetStoreLocatorDraftPagesDto } from '@malou-io/package-dto';

import { GetStoreLocatorStorePagesForEditService } from ':modules/store-locator/services/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.service';

@singleton()
export class GetStoreLocatorStorePagesForEditUseCase {
    constructor(private readonly _getStoreLocatorStorePagesForEditService: GetStoreLocatorStorePagesForEditService) {}

    async execute(organizationId: string): Promise<GetStoreLocatorDraftPagesDto> {
        return await this._getStoreLocatorStorePagesForEditService.execute(organizationId);
    }
}
