import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformKey, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultAttribute } from ':modules/attributes/tests/attribute.builder';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultStoreLocatorOrganizationConfig } from ':modules/store-locator/builders/store-locator-organization-config.builder';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { OrganizationConfigurationAiSettingsUpdate } from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';

describe('StoreLocatorOrganizationConfigRepository', () => {
    beforeAll(() => {
        registerRepositories([
            'AttributesRepository',
            'RestaurantKeywordsRepository',
            'OrganizationsRepository',
            'KeywordsTempRepository',
            'StoreLocatorOrganizationConfigRepository',
        ]);
    });

    describe('updateAiSettings', () => {
        const repository = container.resolve(StoreLocatorOrganizationConfigRepository);

        it('should update AI settings successfully', async () => {
            const restaurantId = newDbId();
            const newAttributeId = 'new_attribute_id';

            const testCase = new TestCaseBuilderV2<
                'attributes' | 'keywordsTemp' | 'restaurantKeywords' | 'organizations' | 'storeLocatorOrganizationConfigs'
            >({
                seeds: {
                    attributes: {
                        data() {
                            return [
                                getDefaultAttribute()
                                    .attributeId('attribute_id_1')
                                    .attributeName({ fr: 'Attribute 1' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                                getDefaultAttribute()
                                    .attributeId('attribute_id_2')
                                    .attributeName({ fr: 'Attribute 2' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                                getDefaultAttribute()
                                    .attributeId(newAttributeId)
                                    .attributeName({
                                        fr: 'New Restaurant Attribute',
                                    })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('keyword text 1').build(),
                                getDefaultKeywordTemp().text('keyword text 2').build(),
                            ];
                        },
                    },
                    organizations: {
                        data() {
                            return [getDefaultOrganization().build()];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            const attributeIds = [dependencies.attributes()[0].attributeId, dependencies.attributes()[1].attributeId];
                            const restaurantKeywordIds = dependencies.restaurantKeywords().map((rk) => rk._id);
                            const organizationId = dependencies.organizations()[0]._id;

                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(organizationId)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds,
                                        restaurantKeywordIds,
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoreLocatorOrganizationConfiguration['aiSettings'] {
                    const attributes = dependencies.attributes;
                    const restaurantKeywords = dependencies.restaurantKeywords;
                    const keywordsTemp = dependencies.keywordsTemp;

                    return {
                        tone: ['friendly', 'professional'],
                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                        attributeIds: attributes.map((a) => a.attributeId),
                        restaurantKeywordIds: restaurantKeywords.map((rk) => rk._id.toString()),
                        specialAttributes: [
                            {
                                restaurantId: restaurantId.toString(),
                                text: 'Special dietary options available',
                            },
                        ],
                        attributes: attributes.map((attribute) => ({
                            id: attribute._id.toString(),
                            attributeId: attribute.attributeId,
                            platformKey: attribute.platformKey,
                            attributeName: {
                                fr: attribute.attributeName.fr,
                                en: attribute.attributeName.en,
                                es: attribute.attributeName.es,
                                it: attribute.attributeName.it,
                            },
                        })),
                        keywords: restaurantKeywords.map((restaurantKeyword) => ({
                            restaurantKeywordId: restaurantKeyword._id.toString(),
                            text: keywordsTemp.find((kt) => kt._id.toString() === restaurantKeyword.keywordId.toString())!.text,
                            restaurantId: restaurantKeyword.restaurantId.toString(),
                            keywordId: restaurantKeyword.keywordId.toString(),
                        })),
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();
            const seededObjects = testCase.getSeededObjects();
            const restaurantKeywordIds = seededObjects.restaurantKeywords.map((rk) => rk._id);
            const attributes = seededObjects.attributes;

            const newAiSettings: OrganizationConfigurationAiSettingsUpdate = {
                tone: ['friendly', 'professional'],
                languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                attributeIds: [attributes[0].attributeId, attributes[1].attributeId, newAttributeId],
                restaurantKeywordIds: restaurantKeywordIds.map((id) => id.toString()),
                specialAttributes: [
                    {
                        restaurantId: restaurantId.toString(),
                        text: 'Special dietary options available',
                    },
                ],
            };

            const organizationId = seededObjects.organizations[0]._id.toString();
            const result = await repository.updateAiSettings(organizationId, newAiSettings);
            expect(result.aiSettings).toEqual(expectedResult);
        });
    });
});
