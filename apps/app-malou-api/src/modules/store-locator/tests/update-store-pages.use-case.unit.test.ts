import { container } from 'tsyringe';

import { UpdateStoreLocatorStorePagesBodyDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultStoreLocatorRestaurantPage } from ':modules/store-locator/builders/store-locator-restaurant-page.builder';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import UpdateStoreLocatorStorePagesUseCase from ':modules/store-locator/use-cases/update-store-pages/update-store-pages.use-case';

describe('UpdateStoreLocatorStorePagesUseCase', () => {
    let useCase: UpdateStoreLocatorStorePagesUseCase;
    let repository: StoreLocatorRestaurantPageRepository;

    beforeAll(() => {
        registerRepositories(['StoreLocatorRestaurantPageRepository']);
        useCase = container.resolve(UpdateStoreLocatorStorePagesUseCase);
        repository = container.resolve(StoreLocatorRestaurantPageRepository);
    });

    it('should raise an error if there is no draft page for the restaurant', async () => {
        const organizationId_1 = newDbId();
        const organizationId_2 = newDbId();

        const restaurantId_1_1 = newDbId();

        const restaurantId_2_1 = newDbId();

        const testCase = new TestCaseBuilderV2<'storeLocatorRestaurantPage'>({
            seeds: {
                storeLocatorRestaurantPage: {
                    data() {
                        return [
                            // French
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_1)
                                .restaurantId(restaurantId_1_1)
                                .lang(StoreLocatorLanguage.FR)
                                .build(),

                            // english
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_1)
                                .restaurantId(restaurantId_1_1)
                                .lang(StoreLocatorLanguage.EN)
                                .build(),

                            // should not be updated
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_2)
                                .restaurantId(restaurantId_2_1)
                                .lang(StoreLocatorLanguage.FR)
                                .build(),
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_2)
                                .restaurantId(restaurantId_2_1)
                                .lang(StoreLocatorLanguage.EN)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const updatePayload: UpdateStoreLocatorStorePagesBodyDto = [
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.FR,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'Image du nouveau restaurant',
                    },
                    title: 'Nouveau nom du restaurant',
                    ctas: [
                        {
                            text: 'Réserver une table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
        ];

        await expect(() =>
            useCase.execute({
                organizationId: organizationId_1.toString(),
                pages: updatePayload,
            })
        ).rejects.toThrow();
    });

    it('should not update other organizations restaurants pages', async () => {
        const organizationId_1 = newDbId();
        const organizationId_2 = newDbId();

        const restaurantId_1_1 = newDbId();

        const restaurantId_2_1 = newDbId();

        const testCase = new TestCaseBuilderV2<'storeLocatorRestaurantPage'>({
            seeds: {
                storeLocatorRestaurantPage: {
                    data() {
                        return [
                            // French
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_1)
                                .restaurantId(restaurantId_1_1)
                                .lang(StoreLocatorLanguage.FR)
                                .build(),

                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_1)
                                .restaurantId(restaurantId_1_1)
                                .lang(StoreLocatorLanguage.FR)
                                .status(StoreLocatorPageStatus.DRAFT)
                                .build(),

                            // english
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_1)
                                .restaurantId(restaurantId_1_1)
                                .lang(StoreLocatorLanguage.EN)
                                .build(),

                            // should not be updated
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_2)
                                .restaurantId(restaurantId_2_1)
                                .lang(StoreLocatorLanguage.FR)
                                .build(),
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId_2)
                                .restaurantId(restaurantId_2_1)
                                .lang(StoreLocatorLanguage.EN)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();

        const updatePayload: UpdateStoreLocatorStorePagesBodyDto = [
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.FR,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'Image du nouveau restaurant',
                    },
                    title: 'Nouveau nom du restaurant',
                    ctas: [
                        {
                            text: 'Réserver une table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
        ];

        await useCase.execute({
            organizationId: organizationId_1.toString(),
            pages: updatePayload,
        });

        const pagesOrg1Count = await repository.countDocuments({
            filter: {
                organizationId: organizationId_1,
            },
        });

        expect(pagesOrg1Count).toBe(3);

        const updatedPagesOrg1 = await repository.find({
            filter: {
                organizationId: organizationId_1,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: {
                lean: true,
            },
        });

        const org2pages = await repository.find({
            filter: {
                organizationId: organizationId_2,
            },
            options: {
                lean: true,
            },
        });

        for (const page of updatedPagesOrg1) {
            if (page.lang === StoreLocatorLanguage.FR) {
                expect(page.blocks.information.title).toBe('Nouveau nom du restaurant');
            }
            if (page.lang === StoreLocatorLanguage.EN) {
                expect(page.blocks.information.title).toBe(seededObjects.storeLocatorRestaurantPage[0].blocks.information.title);
            }
        }

        for (const page of org2pages) {
            if (page.lang === StoreLocatorLanguage.FR) {
                expect(page).toEqual(
                    seededObjects.storeLocatorRestaurantPage.find(
                        (p) =>
                            p.restaurantId.toString() === restaurantId_2_1.toString() &&
                            p.organizationId.toString() === organizationId_2.toString() &&
                            p.lang === StoreLocatorLanguage.FR
                    )
                );
            }
            if (page.lang === StoreLocatorLanguage.EN) {
                expect(page).toEqual(
                    seededObjects.storeLocatorRestaurantPage.find(
                        (p) =>
                            p.restaurantId.toString() === restaurantId_2_1.toString() &&
                            p.organizationId.toString() === organizationId_2.toString() &&
                            p.lang === StoreLocatorLanguage.EN
                    )
                );
            }
        }
    });

    it('should correctly update the information block', async () => {
        const organizationId_1 = newDbId();

        const restaurantId_1_1 = newDbId();
        const restaurantId_1_2 = newDbId();

        const testCase = new TestCaseBuilderV2<'storeLocatorRestaurantPage'>({
            seeds: {
                storeLocatorRestaurantPage: {
                    data() {
                        return [
                            // French
                            ...[restaurantId_1_1, restaurantId_1_2]
                                .map((restaurantId) => {
                                    return [
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.FR)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.FR)
                                            .status(StoreLocatorPageStatus.DRAFT)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.EN)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.EN)
                                            .status(StoreLocatorPageStatus.DRAFT)
                                            .build(),
                                    ];
                                })
                                .flat(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                const { storeLocatorRestaurantPage } = dependencies;
                return {
                    storeLocatorRestaurantPage: storeLocatorRestaurantPage
                        .filter(({ status }) => status === StoreLocatorPageStatus.DRAFT)
                        .map((page) => {
                            if (page.restaurantId.toString() === restaurantId_1_1.toString()) {
                                return {
                                    ...page,
                                    status: StoreLocatorPageStatus.DRAFT,
                                    blocks: {
                                        ...page.blocks,
                                        information: {
                                            ...page.blocks.information,
                                            title:
                                                page.lang === StoreLocatorLanguage.FR ? 'Nouveau nom du restaurant' : 'New Restaurant Name',
                                            image: {
                                                description:
                                                    page.lang === StoreLocatorLanguage.FR
                                                        ? 'Image du nouveau restaurant'
                                                        : 'A beautiful restaurant image',
                                                url: 'https://example.com/new-image.jpg',
                                            },
                                            ctas: [
                                                {
                                                    text: page.lang === StoreLocatorLanguage.FR ? 'Réserver une table' : 'Book a table',
                                                    url: 'https://example.com/reservation',
                                                },
                                            ],
                                        },
                                    },
                                };
                            }
                            return page;
                        }),
                };
            },
        });

        await testCase.build();

        const expectedResult = testCase.getExpectedResult();
        const updatePayload: UpdateStoreLocatorStorePagesBodyDto = [
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.FR,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'Image du nouveau restaurant',
                    },
                    title: 'Nouveau nom du restaurant',
                    ctas: [
                        {
                            text: 'Réserver une table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.EN,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'A beautiful restaurant image',
                    },
                    title: 'New Restaurant Name',
                    ctas: [
                        {
                            text: 'Book a table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
        ];

        await useCase.execute({
            organizationId: organizationId_1.toString(),
            pages: updatePayload,
        });

        const updatedPagesOrg1 = await repository.find({
            filter: {
                organizationId: organizationId_1,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: {
                lean: true,
            },
        });

        for (const page of updatedPagesOrg1) {
            expect({ ...page, updatedAt: expect.any(Date), _id: null, createdAt: expect.any(Date) }).toEqual({
                ...expectedResult.storeLocatorRestaurantPage.find(
                    (p) => p.restaurantId.toString() === page.restaurantId.toString() && p.lang === page.lang
                ),
                createdAt: expect.any(Date),
                updatedAt: expect.any(Date),
                _id: null,
            });
        }
    });

    it('should correctly update the draft', async () => {
        const organizationId_1 = newDbId();

        const restaurantId_1_1 = newDbId();
        const restaurantId_1_2 = newDbId();

        const testCase = new TestCaseBuilderV2<'storeLocatorRestaurantPage'>({
            seeds: {
                storeLocatorRestaurantPage: {
                    data() {
                        return [
                            ...[restaurantId_1_1, restaurantId_1_2]
                                .map((restaurantId) => {
                                    return [
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.FR)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.FR)
                                            .status(StoreLocatorPageStatus.DRAFT)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.EN)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.EN)
                                            .status(StoreLocatorPageStatus.DRAFT)
                                            .build(),
                                    ];
                                })
                                .flat(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                const { storeLocatorRestaurantPage } = dependencies;
                return {
                    storeLocatorRestaurantPage: storeLocatorRestaurantPage
                        .filter(({ status }) => status === StoreLocatorPageStatus.DRAFT)
                        .map((page) => {
                            if (page.restaurantId.toString() === restaurantId_1_1.toString()) {
                                return {
                                    ...page,
                                    status: StoreLocatorPageStatus.DRAFT,
                                    blocks: {
                                        ...page.blocks,
                                        information: {
                                            ...page.blocks.information,
                                            title:
                                                page.lang === StoreLocatorLanguage.FR ? 'Nouveau nom du restaurant' : 'New Restaurant Name',
                                            image: {
                                                description:
                                                    page.lang === StoreLocatorLanguage.FR
                                                        ? 'Image du nouveau restaurant'
                                                        : 'A beautiful restaurant image',
                                                url: 'https://example.com/new-image.jpg',
                                            },
                                            ctas: [
                                                {
                                                    text: page.lang === StoreLocatorLanguage.FR ? 'Réserver une table' : 'Book a table',
                                                    url: 'https://example.com/reservation',
                                                },
                                            ],
                                        },
                                    },
                                };
                            }
                            return page;
                        }),
                };
            },
        });

        await testCase.build();

        const expectedResult = testCase.getExpectedResult();
        const updatePayload: UpdateStoreLocatorStorePagesBodyDto = [
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.FR,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'Image du nouveau restaurant',
                    },
                    title: 'Nouveau nom du restaurant',
                    ctas: [
                        {
                            text: 'Réserver une table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.EN,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'A beautiful restaurant image',
                    },
                    title: 'New Restaurant Name',
                    ctas: [
                        {
                            text: 'Book a table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
        ];

        const alreadyExistingDraft = await repository.findOne({
            filter: {
                organizationId: organizationId_1,
                restaurantId: restaurantId_1_1,
                lang: StoreLocatorLanguage.FR,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: {
                lean: true,
            },
        });

        await useCase.execute({
            organizationId: organizationId_1.toString(),
            pages: updatePayload,
        });

        const updatedPagesOrg1 = await repository.find({
            filter: {
                organizationId: organizationId_1,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: {
                lean: true,
            },
        });

        for (const page of updatedPagesOrg1) {
            if (page.restaurantId.toString() === restaurantId_1_1.toString() && page.lang === StoreLocatorLanguage.FR) {
                expect({ ...page, updatedAt: expect.any(Date) }).toEqual({
                    ...expectedResult.storeLocatorRestaurantPage.find(
                        (p) => p.restaurantId.toString() === page.restaurantId.toString() && p.lang === page.lang
                    ),
                    _id: alreadyExistingDraft?._id,
                    updatedAt: expect.any(Date),
                });
            } else {
                expect({ ...page, updatedAt: expect.any(Date), _id: null, createdAt: expect.any(Date) }).toEqual({
                    ...expectedResult.storeLocatorRestaurantPage.find(
                        (p) => p.restaurantId.toString() === page.restaurantId.toString() && p.lang === page.lang
                    ),
                    createdAt: expect.any(Date),
                    updatedAt: expect.any(Date),
                    _id: null,
                });
            }
        }
    });

    it('should correctly update all the blocks', async () => {
        const organizationId_1 = newDbId();

        const restaurantId_1_1 = newDbId();
        const restaurantId_1_2 = newDbId();

        const testCase = new TestCaseBuilderV2<'storeLocatorRestaurantPage'>({
            seeds: {
                storeLocatorRestaurantPage: {
                    data() {
                        return [
                            ...[restaurantId_1_1, restaurantId_1_2]
                                .map((restaurantId) => {
                                    return [
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.FR)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.FR)
                                            .status(StoreLocatorPageStatus.DRAFT)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.EN)
                                            .build(),
                                        getDefaultStoreLocatorRestaurantPage()
                                            .organizationId(organizationId_1)
                                            .restaurantId(restaurantId)
                                            .lang(StoreLocatorLanguage.EN)
                                            .status(StoreLocatorPageStatus.DRAFT)
                                            .build(),
                                    ];
                                })
                                .flat(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                const { storeLocatorRestaurantPage } = dependencies;
                return {
                    storeLocatorRestaurantPage: storeLocatorRestaurantPage
                        .filter(({ status }) => status === StoreLocatorPageStatus.DRAFT)
                        .map((page) => {
                            if (page.restaurantId.toString() === restaurantId_1_1.toString()) {
                                return {
                                    ...page,
                                    status: StoreLocatorPageStatus.DRAFT,
                                    blocks: {
                                        ...page.blocks,
                                        information: {
                                            ...page.blocks.information,
                                            title:
                                                page.lang === StoreLocatorLanguage.FR ? 'Nouveau nom du restaurant' : 'New Restaurant Name',
                                            image: {
                                                description:
                                                    page.lang === StoreLocatorLanguage.FR
                                                        ? 'Image du nouveau restaurant'
                                                        : 'A beautiful restaurant image',
                                                url: 'https://example.com/new-image.jpg',
                                            },
                                            ctas: [
                                                {
                                                    text: page.lang === StoreLocatorLanguage.FR ? 'Réserver une table' : 'Book a table',
                                                    url: 'https://example.com/reservation',
                                                },
                                            ],
                                        },
                                        gallery:
                                            page.lang === StoreLocatorLanguage.FR
                                                ? {
                                                      title: 'Nouvelle galerie',
                                                      subtitle: 'Nos nouvelles photos',
                                                      images: [
                                                          {
                                                              url: 'https://example.com/new-gallery-image.jpg',
                                                              description: 'Une nouvelle image de la galerie',
                                                          },
                                                      ],
                                                  }
                                                : {
                                                      title: 'New gallery',
                                                      subtitle: 'Our new photos',
                                                      images: [
                                                          {
                                                              url: 'https://example.com/new-gallery-image.jpg',
                                                              description: 'A new gallery image',
                                                          },
                                                      ],
                                                  },
                                        reviews:
                                            page.lang === StoreLocatorLanguage.FR
                                                ? {
                                                      title: 'Nouvelles critiques',
                                                      cta: {
                                                          text: 'Laisser une nouvelle critique',
                                                          url: 'https://example.com/new-review',
                                                      },
                                                  }
                                                : {
                                                      title: 'New reviews',
                                                      cta: {
                                                          text: 'Leave a new review',
                                                          url: 'https://example.com/new-review',
                                                      },
                                                  },
                                        callToActions:
                                            page.lang === StoreLocatorLanguage.FR
                                                ? {
                                                      title: 'Nouvelles actions',
                                                      ctas: [
                                                          {
                                                              text: 'Nouvelle action',
                                                              url: 'https://example.com/new-action',
                                                          },
                                                          {
                                                              text: 'Autre action',
                                                              url: 'https://example.com/another-action',
                                                          },
                                                      ],
                                                  }
                                                : {
                                                      title: 'New actions',
                                                      ctas: [
                                                          {
                                                              text: 'New action',
                                                              url: 'https://example.com/new-action',
                                                          },
                                                          {
                                                              text: 'Another action',
                                                              url: 'https://example.com/another-action',
                                                          },
                                                      ],
                                                  },
                                        descriptions:
                                            page.lang === StoreLocatorLanguage.FR
                                                ? {
                                                      items: [
                                                          {
                                                              title: 'Nouvelle description',
                                                              image: {
                                                                  url: 'https://example.com/new-description-image.jpg',
                                                                  description: 'Image de la nouvelle description',
                                                              },
                                                              blocks: [
                                                                  {
                                                                      text: 'Texte de la nouvelle description',
                                                                      title: 'Titre de la nouvelle description',
                                                                  },
                                                              ],
                                                          },
                                                      ],
                                                  }
                                                : {
                                                      items: [
                                                          {
                                                              title: 'New description',
                                                              image: {
                                                                  url: 'https://example.com/new-description-image.jpg',
                                                                  description: 'New description image',
                                                              },
                                                              blocks: [
                                                                  {
                                                                      text: 'New description text',
                                                                      title: 'New description title',
                                                                  },
                                                              ],
                                                          },
                                                      ],
                                                  },
                                        socialNetworks: {
                                            ...page.blocks.socialNetworks,
                                            title:
                                                page.lang === StoreLocatorLanguage.FR ? 'Nouveaux réseaux sociaux' : 'New social networks',
                                        },
                                    },
                                };
                            }
                            if (page.restaurantId.toString() === restaurantId_1_2.toString()) {
                                return {
                                    ...page,
                                    status: StoreLocatorPageStatus.DRAFT,
                                    blocks: {
                                        ...page.blocks,
                                        information: {
                                            ...page.blocks.information,
                                            title:
                                                page.lang === StoreLocatorLanguage.FR ? 'Nouveau nom du restaurant' : 'New Restaurant Name',
                                            image: {
                                                ...page.blocks.information.image,
                                                description:
                                                    page.lang === StoreLocatorLanguage.FR
                                                        ? 'Image du nouveau restaurant'
                                                        : 'A beautiful restaurant image',
                                                url: 'https://example.com/new-image.jpg',
                                            },
                                            ctas: [
                                                {
                                                    text: page.lang === StoreLocatorLanguage.FR ? 'Réserver une table' : 'Book a table',
                                                    url: 'https://example.com/reservation',
                                                },
                                            ],
                                        },
                                    },
                                };
                            }
                            return page;
                        }),
                };
            },
        });

        await testCase.build();

        const expectedResult = testCase.getExpectedResult();
        const updatePayload: UpdateStoreLocatorStorePagesBodyDto = [
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.FR,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'Image du nouveau restaurant',
                    },
                    title: 'Nouveau nom du restaurant',
                    ctas: [
                        {
                            text: 'Réserver une table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
                gallery: {
                    title: 'Nouvelle galerie',
                    subtitle: 'Nos nouvelles photos',
                    images: [
                        {
                            url: 'https://example.com/new-gallery-image.jpg',
                            description: 'Une nouvelle image de la galerie',
                        },
                    ],
                },
                reviews: {
                    title: 'Nouvelles critiques',
                    cta: {
                        text: 'Laisser une nouvelle critique',
                        url: 'https://example.com/new-review',
                    },
                },
                callToActions: {
                    title: 'Nouvelles actions',
                    links: [
                        {
                            text: 'Nouvelle action',
                            url: 'https://example.com/new-action',
                        },
                        {
                            text: 'Autre action',
                            url: 'https://example.com/another-action',
                        },
                    ],
                },
                descriptions: {
                    items: [
                        {
                            title: 'Nouvelle description',
                            image: {
                                url: 'https://example.com/new-description-image.jpg',
                                description: 'Image de la nouvelle description',
                            },
                            blocks: [
                                {
                                    text: 'Texte de la nouvelle description',
                                    title: 'Titre de la nouvelle description',
                                },
                            ],
                        },
                    ],
                },
                socialNetworks: {
                    title: 'Nouveaux réseaux sociaux',
                },
            },
            {
                restaurantId: restaurantId_1_1.toString(),
                lang: StoreLocatorLanguage.EN,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'A beautiful restaurant image',
                    },
                    title: 'New Restaurant Name',
                    ctas: [
                        {
                            text: 'Book a table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
                gallery: {
                    title: 'New gallery',
                    subtitle: 'Our new photos',
                    images: [
                        {
                            url: 'https://example.com/new-gallery-image.jpg',
                            description: 'A new gallery image',
                        },
                    ],
                },
                reviews: {
                    title: 'New reviews',
                    cta: {
                        text: 'Leave a new review',
                        url: 'https://example.com/new-review',
                    },
                },
                callToActions: {
                    title: 'New actions',
                    links: [
                        {
                            text: 'New action',
                            url: 'https://example.com/new-action',
                        },
                        {
                            text: 'Another action',
                            url: 'https://example.com/another-action',
                        },
                    ],
                },
                descriptions: {
                    items: [
                        {
                            title: 'New description',
                            image: {
                                url: 'https://example.com/new-description-image.jpg',
                                description: 'New description image',
                            },
                            blocks: [
                                {
                                    text: 'New description text',
                                    title: 'New description title',
                                },
                            ],
                        },
                    ],
                },
                socialNetworks: {
                    title: 'New social networks',
                },
            },
            {
                restaurantId: restaurantId_1_2.toString(),
                lang: StoreLocatorLanguage.FR,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'Image du nouveau restaurant',
                    },
                    title: 'Nouveau nom du restaurant',
                    ctas: [
                        {
                            text: 'Réserver une table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
            {
                restaurantId: restaurantId_1_2.toString(),
                lang: StoreLocatorLanguage.EN,
                information: {
                    image: {
                        url: 'https://example.com/new-image.jpg',
                        description: 'A beautiful restaurant image',
                    },
                    title: 'New Restaurant Name',
                    ctas: [
                        {
                            text: 'Book a table',
                            url: 'https://example.com/reservation',
                        },
                    ],
                },
            },
        ];

        await useCase.execute({
            organizationId: organizationId_1.toString(),
            pages: updatePayload,
        });

        const updatedPagesOrg1 = await repository.find({
            filter: {
                organizationId: organizationId_1,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: {
                lean: true,
            },
        });

        for (const page of updatedPagesOrg1) {
            expect({ ...page, updatedAt: expect.any(Date), createdAt: expect.any(Date), _id: null }).toEqual({
                ...expectedResult.storeLocatorRestaurantPage.find(
                    (p) => p.restaurantId.toString() === page.restaurantId.toString() && p.lang === page.lang
                ),
                updatedAt: expect.any(Date),
                createdAt: expect.any(Date),
                _id: null,
            });
        }
    });
});
