import { singleton } from 'tsyringe';

import { DbId, IRestaurant, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

export interface SearchRestaurantsV2Params {
    text?: string;
    fields?: string[];
    limit?: number;
    offset?: number;
    active?: boolean;
    userId?: string;
}

export interface SearchRestaurantsV2Result {
    data: Partial<IRestaurant>[];
    total: number;
}

@singleton()
export class SearchRestaurantsV2UseCase {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(params: SearchRestaurantsV2Params): Promise<SearchRestaurantsV2Result> {
        const { text, fields = [], limit, offset, active, userId } = params;

        const populateOrganizationStage = [
            {
                $lookup: {
                    from: 'organizations',
                    localField: 'organizationId',
                    foreignField: '_id',
                    as: 'organization',
                },
            },
            {
                $unwind: {
                    path: '$organization',
                },
            },
        ];

        const filter: any = {};

        if (text?.trim()) {
            const searchText = text.trim();

            const diacriticInsensitiveText = toDiacriticInsensitiveRegexString(searchText);

            let dbId: DbId | null = null;

            try {
                dbId = toDbId(searchText);
            } catch (err) {
                // ignore
            }

            filter.$or = [
                { name: { $regex: diacriticInsensitiveText, $options: 'i' } },
                { internalName: { $regex: diacriticInsensitiveText, $options: 'i' } },
                { 'address.formattedAddress': { $regex: diacriticInsensitiveText, $options: 'i' } },
                { 'address.locality': { $regex: diacriticInsensitiveText, $options: 'i' } },
                { 'organization.name': { $regex: diacriticInsensitiveText, $options: 'i' } },
            ];

            if (dbId) {
                filter.$or.push({ _id: dbId });
            }
        }

        if (active !== undefined) {
            filter.active = active;
        }

        let fieldsToProject = {
            name: { $first: '$name' },
            address: { $first: '$address' },
        };
        if (fields.length > 0) {
            fieldsToProject = { ...fieldsToProject, ...fields.reduce((acc, field) => ({ ...acc, [field]: { $first: `$${field}` } }), {}) };
        }

        const populateManagersStage = [
            {
                $lookup: {
                    from: 'userrestaurants',
                    localField: '_id',
                    foreignField: 'restaurantId',
                    as: 'userRestaurant',
                },
            },
            { $unwind: { path: '$userRestaurant', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userRestaurant.userId',
                    foreignField: '_id',
                    as: 'manager',
                    pipeline: [{ $project: { _id: 1, email: 1, name: 1, lastname: 1 } }],
                },
            },
            { $unwind: { path: '$manager', preserveNullAndEmptyArrays: true } },
            {
                $group: {
                    _id: '$_id',
                    ...fieldsToProject,
                    createdAt: { $first: '$createdAt' },
                    organization: { $first: '$organization' },
                    managers: {
                        $push: {
                            $cond: [
                                { $gt: ['$manager', null] },
                                {
                                    restaurantId: '$_id',
                                    userId: '$userRestaurant.userId',
                                    user: '$manager',
                                    caslRole: '$userRestaurant.caslRole',
                                },
                                '$$REMOVE',
                            ],
                        },
                    },
                    isOwnedByUser: {
                        $max: {
                            $cond: [{ $eq: ['$userRestaurant.userId', userId ? toDbId(userId) : null] }, 1, 0],
                        },
                    },
                },
            },
        ];

        const pipeline: any[] = [...populateOrganizationStage, { $match: filter }];

        // i
        if (userId) {
            pipeline.push(
                {
                    $lookup: {
                        from: 'userrestaurants',
                        localField: '_id',
                        foreignField: 'restaurantId',
                        as: 'userRestaurantCheck',
                        pipeline: [{ $match: { userId: toDbId(userId) } }, { $project: { _id: 1 } }],
                    },
                },
                {
                    $addFields: {
                        isOwnedByUser: { $cond: [{ $gt: [{ $size: '$userRestaurantCheck' }, 0] }, 1, 0] },
                    },
                },
                {
                    $sort: {
                        isOwnedByUser: -1,
                        createdAt: -1,
                        name: 1,
                    },
                }
            );
        } else {
            pipeline.push({
                $sort: {
                    createdAt: -1,
                    name: 1,
                },
            });
        }

        if (isNotNil(offset)) {
            pipeline.push({ $skip: offset });
        }

        if (isNotNil(limit)) {
            pipeline.push({ $limit: limit });
        }

        pipeline.push(...populateManagersStage);

        if (userId) {
            // we need to sort again because of the $group stage that reorders the documents
            pipeline.push({
                $sort: {
                    isOwnedByUser: -1,
                    createdAt: -1,
                    name: 1,
                    _id: 1,
                },
            });
        } else {
            pipeline.push({
                $sort: {
                    createdAt: -1,
                    name: 1,
                    _id: 1,
                },
            });
        }

        const countPipeline = [...populateOrganizationStage, { $match: filter }, { $count: 'total' }];

        const [restaurants, totalCount] = await Promise.all([
            this._restaurantsRepository.aggregate(pipeline, {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            }),
            this._restaurantsRepository.aggregate(countPipeline, {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            }),
        ]);

        return {
            data: restaurants,
            total: totalCount[0]?.total ?? 0,
        };
    }
}
