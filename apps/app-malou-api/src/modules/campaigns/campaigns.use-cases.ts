import { chunk, countBy, Dictionary } from 'lodash';
import { autoInjectable } from 'tsyringe';

import { CreateCampaignDto } from '@malou-io/package-dto';
import { DbId, ICampaign, ID, toDbId } from '@malou-io/package-models';
import {
    ContactMode,
    EmailCategory,
    EmailType,
    MalouErrorCode,
    PlatformKey,
    RedirectStar,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import ClientsRepository from ':modules/clients/clients.repository';
import MailingUseCases from ':modules/mailing/use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

import { Platform } from '../platforms/platforms.entity';
import CampaignsRepository from './campaigns.repository';

type ICampaignContactInteractions = ICampaign['contactInteractions'];

@autoInjectable()
export default class CampaignsUseCases {
    constructor(
        private _campaignsRepository: CampaignsRepository,
        private _platformsRepository: PlatformsRepository,
        private _clientsRepository: ClientsRepository,
        private _restaurantsRepository: RestaurantsRepository,
        private _mailingUseCases: MailingUseCases
    ) {}

    async getCampaignsForRestaurant(restaurantId: DbId) {
        return this._campaignsRepository.find({ filter: { restaurantId }, options: { lean: true } });
    }

    async createCampaign(data: CreateCampaignDto): Promise<ICampaign> {
        return this._campaignsRepository.createCampaign(data);
    }

    async sendCampaignEmails(campaignId: string, restaurantId: string): Promise<void> {
        const campaign = await this._campaignsRepository.findOne({ filter: { _id: campaignId, restaurantId } });
        if (!campaign) {
            logger.warn('[SEND_CAMPAIGN_MAILS] Campaign not found', { campaignId, restaurantId });
            throw new MalouError(MalouErrorCode.CAMPAIGN_NOT_FOUND);
        }

        const canSendMails = new Date(campaign.startDate).getTime() <= new Date().getTime();
        if (canSendMails) {
            await this.sendEmailsToClients(campaign);
        }
    }

    async deleteCampaignsByIds(campaignIds: ID[]): Promise<void> {
        await this._campaignsRepository.deleteMany({ filter: { _id: { $in: campaignIds } } });
    }

    async deleteCampaignById(campaignId: DbId): Promise<void> {
        await this._campaignsRepository.deleteOne({ filter: { _id: campaignId } });
    }

    async updateCampaignEndDate(campaignId: DbId, endDate: Date = new Date()) {
        return this._campaignsRepository.findOneAndUpdate({ filter: { _id: campaignId }, update: { endDate }, options: { lean: true } });
    }

    async getCampaignSources(campaignId: DbId): Promise<Dictionary<number>> {
        const campaign = await this._campaignsRepository.findOneOrFail({
            filter: { _id: campaignId },
            projection: { 'contactInteractions.clientId': 1 },
        });

        const clientsSources = await this._clientsRepository.find({
            filter: { _id: { $in: campaign.contactInteractions.map((c) => c.clientId) } },
            projection: { source: 1 },
        });
        return countBy(clientsSources, 'source');
    }

    async updateClientUnsubscribedFromCampaign(clientId: DbId, contactMode: ContactMode) {
        await this._clientsRepository.findOneAndUpdate({ filter: { _id: clientId }, update: { $pull: { accepts: contactMode } } });
    }

    async initRedirectFrom(campaignId: ID, clientId: ID, starNb: number): Promise<string | null> {
        // If test environment
        if ([campaignId, clientId].includes('test')) {
            return null;
        }

        const update = {
            'contactInteractions.$.lastStarRatingDate': new Date(),
            'contactInteractions.$.lastStarRating': starNb,
        };

        const campaign = await this._campaignsRepository.findOneAndUpdate({
            filter: {
                _id: campaignId,
                'contactInteractions.clientId': clientId,
            },
            update,
            options: { lean: true },
        });

        return campaign?.redirectFrom ?? null;
    }

    async getRestaurantNameById(restaurantId: string) {
        return this._restaurantsRepository.findOneOrFail({ filter: { _id: toDbId(restaurantId) }, projection: { name: 1 } });
    }

    async sendReviewBoosterTestEmail(
        restaurantId: string,
        userId: DbId,
        platformKey: string,
        emailData: {
            from: { name: string; email: string };
            to: string;
            object: string;
            message: string;
        }
    ): Promise<void> {
        Object.assign(emailData, { platformKey });
        await this._mailingUseCases.sendEmail(EmailCategory.REVIEW_BOOSTER, EmailType.REVIEW_BOOSTER_TEST, {
            emailData,
            restaurantId,
            userId: userId.toString(),
        });
    }

    async saveUnsubscribedClientInCampaign(campaignId: string, clientId: string) {
        await this._campaignsRepository.findOneAndUpdate({
            filter: { _id: toDbId(campaignId), 'contactInteractions.clientId': toDbId(clientId) },
            update: {
                'contactInteractions.$.unsubscribedDate': new Date(),
            },
        });
    }

    async handleCampaignOpenedEmail(mailId: string): Promise<any> {
        return this._campaignsRepository.findOneAndUpdate({
            filter: { contactInteractions: { $elemMatch: { awsMailId: mailId, openedDate: null } } },
            update: {
                'contactInteractions.$.openedDate': new Date(),
            },
        });
    }

    async handleCampaignDeliveredEmail(mailId: string): Promise<any> {
        return this._campaignsRepository.findOneAndUpdate({
            filter: { 'contactInteractions.awsMailId': mailId },
            update: { 'contactInteractions.$.deliveredDate': new Date() },
        });
    }

    async handleCampaignBounceEmail(mailId: string, bounceType: string): Promise<void> {
        const campaign = await this._campaignsRepository.findOneAndUpdate({
            filter: { 'contactInteractions.awsMailId': mailId },
            update: { 'contactInteractions.$.deliveredDate': null },
        });
        if (!campaign) {
            return;
        }

        if (bounceType === 'Permanent') {
            // should go to "suppression list" if permanent = not accepting emails
            const clientId = campaign.contactInteractions?.find((client) => client.awsMailId === mailId)?.clientId;
            if (clientId) {
                await this._clientsRepository.findOneAndUpdate({
                    filter: { _id: clientId },
                    update: {
                        $pull: { accepts: ContactMode.EMAIL },
                    },
                });
            }
        }
    }

    async handleCampaignComplaintEmail(mailId: string): Promise<void> {
        const campaign = await this._campaignsRepository.findOneAndUpdate({
            filter: { 'contactInteractions.awsMailId': mailId },
            update: { 'contactInteractions.$.unsubscribedDate': new Date() },
        });
        if (!campaign) {
            return;
        }
        const clientId = campaign?.contactInteractions?.find((client) => client.awsMailId === mailId)?.clientId;
        if (clientId) {
            await this._clientsRepository.findOneAndUpdate({
                filter: { _id: clientId },
                update: {
                    $pull: { accepts: ContactMode.EMAIL },
                },
            });
        }
    }

    async sendEmailsToClients(campaign: ICampaign): Promise<void> {
        const emailPerSeconds = 5;

        const clientsChunks: ICampaignContactInteractions[] = chunk(campaign.contactInteractions, emailPerSeconds);

        for (const clients of clientsChunks) {
            for (const client of clients) {
                try {
                    await this._mailingUseCases.sendEmail(EmailCategory.REVIEW_BOOSTER, EmailType.REVIEW_BOOSTER, {
                        restaurantId: campaign.restaurantId.toString(),
                        campaignId: campaign._id.toString(),
                        clientId: client.clientId.toString(),
                    });
                    await this._clientsRepository.findOneAndUpdate({
                        filter: { _id: client.clientId },
                        update: {
                            $set: { lastContactedAt: new Date() },
                            $inc: { contactCount: 1 },
                        },
                    });
                } catch (err) {
                    logger.error('[sendEmailsToClients]', err);
                }
            }
            await waitFor(TimeInMilliseconds.SECOND);
        }
    }

    async getRedirectionUrl(params: {
        clientId: string;
        restaurantId: string;
        campaignId: string;
        starNb: number;
        redirectFrom: string | null;
        platformKey: PlatformKey;
        apiKey: string;
        langs?: string[];
    }): Promise<string | undefined> {
        const { clientId, restaurantId, campaignId, starNb, redirectFrom, platformKey, apiKey, langs } = params;
        const platform = await this._platformsRepository.findOneOrFail({
            filter: { restaurantId: toDbId(restaurantId), key: platformKey },
            options: { lean: true },
        });

        const platformRedirectionUrl = new Platform(platform).getRedirectionUrl(langs);

        if ([campaignId, clientId].includes('test')) {
            return starNb < 4
                ? // eslint-disable-next-line max-len
                  `${Config.baseAppUrl}/campaign_review/give_review?client_id=${clientId}&restaurant_id=${restaurantId}&campaign_id=${campaignId}&api_key=${apiKey}&star=${starNb}`
                : platformRedirectionUrl;
        }

        if (starNb < 4 || (redirectFrom === RedirectStar.FIVE_STARS && starNb !== 5)) {
            // eslint-disable-next-line max-len
            return `${Config.baseAppUrl}/campaign_review/give_review?client_id=${clientId}&restaurant_id=${restaurantId}&campaign_id=${campaignId}&api_key=${apiKey}&star=${starNb}`;
        }

        return platformRedirectionUrl;
    }
}
