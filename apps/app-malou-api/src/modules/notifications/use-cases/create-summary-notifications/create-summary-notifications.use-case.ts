import { partition } from 'lodash';
import { DateTime } from 'luxon';
import { inject, registry, singleton } from 'tsyringe';

import { NotificationType } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { Notification } from ':modules/notifications/entities/notification.entity';
import { SummaryNotification } from ':modules/notifications/entities/summary-notification.entity';
import NotificationsRepository, { ISummaryNotification } from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { SendNotificationsToChannelsService } from ':modules/notifications/services/notifications-sender-service/send-notifications-to-channels.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';

@registry([
    {
        token: InjectionToken.NotificationSenderService,
        useClass: SendNotificationsToChannelsService,
    },
])
@singleton()
export class CreateSummaryNotificationsUseCase {
    constructor(
        private readonly _notificationsRepository: NotificationsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        @inject(InjectionToken.NotificationSenderService)
        private readonly _sendNotificationsToChannelsService: NotificationSenderService
    ) {}

    async execute(testUserId?: string): Promise<void> {
        const cursor = await this._notificationsRepository.getNotificationsToSummarize();

        await cursor.eachAsync(
            async (usersWithNotifications) => {
                const notifications: SummaryNotification[] = [];
                for (const userWithNotifications of usersWithNotifications) {
                    const notifs = await this._createAndSendNotificationForUser(userWithNotifications, testUserId);
                    notifications.push(...notifs);
                }
                await Promise.all(notifications.map((n) => this._notificationsRepository.createSummaryNotification(n)));
                await this._sendNotificationsToChannelsService.sendNotificationsToChannels(notifications);
            },
            {
                batchSize: 100,
            }
        );
    }

    private async _createAndSendNotificationForUser(
        { userId, user, notifications }: ISummaryNotification,
        testUserId?: string
    ): Promise<SummaryNotification[]> {
        if (testUserId && userId.toString() !== testUserId) {
            return [];
        }

        const isAvailable = await isFeatureAvailableForUser({
            userId: userId.toString(),
            featureName: 'release-notifications-email-summary',
        });
        if (!isAvailable) {
            return [];
        }

        const notificationsEntities: Notification[] = notifications.map(
            (n) => new Notification(this._notificationsRepository.toNotificationEntityData({ ...n, user }))
        );

        const [expiredNotifications, validNotifications] = partition(notificationsEntities, (n) => this._isNotificationExpired(n));
        await this._markNotificationsAsRead(expiredNotifications.map((n) => n.id));

        const restaurants = await this._getNotificationsRestaurants(notificationsEntities);
        const notificationUser = validNotifications[0].user;

        const summaryNotifications = notificationUser
            .getReceivableNotificationChannels(NotificationType.SUMMARY)
            .map((channel) => SummaryNotification.fromNotifications(validNotifications, restaurants, channel));

        return summaryNotifications;
    }

    private async _getNotificationsRestaurants(notifications: Notification[]): Promise<{ id: string; name: string }[]> {
        const restaurantIds = [...new Set(notifications.map((n) => n.data.restaurantIds ?? []).flat())];
        const restaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: restaurantIds } },
            projection: { name: 1, _id: 1 },
            options: { lean: true },
        });
        return restaurants.map((r) => ({ id: r._id.toString(), name: r.name }));
    }

    private _isNotificationExpired(notification: Notification): boolean {
        const calendarEventsBasedNotifications = [NotificationType.SPECIAL_HOUR, NotificationType.POST_SUGGESTION];

        if (calendarEventsBasedNotifications.includes(notification.type)) {
            const { startDate } = notification.data.event;
            return DateTime.fromJSDate(startDate).startOf('day') <= DateTime.now().startOf('day');
        }

        return false;
    }

    private async _markNotificationsAsRead(notificationIds: string[]): Promise<void> {
        await this._notificationsRepository.updateNotifications({
            notificationIds,
            update: { readAt: DateTime.now().toJSDate() },
        });
    }
}
