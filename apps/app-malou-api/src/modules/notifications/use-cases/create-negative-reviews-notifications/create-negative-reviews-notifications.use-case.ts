import { groupBy, partition, uniq } from 'lodash';
import { inject, registry, singleton } from 'tsyringe';

import { IReview } from '@malou-io/package-models';
import { isNotNil, MediaType, NotificationChannel, NotificationType } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { ReviewNotificationUser } from ':modules/notifications/entities/child-entities/review-notification-user.entity';
import {
    NegativeReviewNotification,
    NegativeReviewNotificationData,
} from ':modules/notifications/entities/negative-review-notification.entity';
import { ReviewNotificationsUserRepository } from ':modules/notifications/repositories/notifications-user-restaurants/review-notifications-user.repository';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { SendNotificationsToChannelsService } from ':modules/notifications/services/notifications-sender-service/send-notifications-to-channels.service';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { isFeatureAvailableForUsers } from ':services/experimentations-service/experimentation.service';

type ReviewsGroupedByRestaurantId = { [restaurantId: string]: IReview[] };

@registry([
    {
        token: InjectionToken.NotificationSenderService,
        useClass: SendNotificationsToChannelsService,
    },
])
@singleton()
export class CreateNegativeReviewsNotificationsUseCase {
    constructor(
        @inject(InjectionToken.NotificationSenderService)
        private readonly _sendNotificationsToChannelsService: NotificationSenderService,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _notificationsRepository: NotificationsRepository,
        private readonly _reviewNotificationsUserRepository: ReviewNotificationsUserRepository
    ) {}

    async execute(testUserId?: string): Promise<NegativeReviewNotification[]> {
        const notifications = await this._getNegativeReviewsNotifications(testUserId);
        if (!notifications.length) {
            return [];
        }

        logger.info(`[SendNegativeReviewsNotificationsUseCase] - Sending ${notifications.length} negative reviews notifications`);

        await this._notificationsRepository.createManyNegativeReviewsNotifications(notifications);
        await this._sendNotificationsToChannelsService.sendNotificationsToChannels(notifications);

        return notifications;
    }

    private async _getNegativeReviewsNotifications(testUserId?: string): Promise<NegativeReviewNotification[]> {
        const users = await this._getUsersToNotify(testUserId);
        const reviewsGroupedByRestaurantId = await this._getNegativeReviewsToBeNotifiedGroupedByRestaurantId();

        const notificationPromises = users.map(async (u) => {
            const userRestaurantsReviews = this._getAllRestaurantsReviewsForUser(u.restaurants, reviewsGroupedByRestaurantId);
            const userReceivableChannels = u.user.getReceivableNotificationChannels(NotificationType.REVIEW_REPLY_REMINDER);
            if (!userRestaurantsReviews.length || userReceivableChannels.length === 0) {
                return null;
            }

            const promises = userReceivableChannels.map((channel) => this._getNotificationForUser(u, userRestaurantsReviews, channel));
            return Promise.all(promises);
        });

        const notificationsToCreate = (await Promise.all(notificationPromises)).flat();
        const notificationsToCreateFiltered = notificationsToCreate.filter(isNotNil);
        return notificationsToCreateFiltered;
    }

    private async _getUsersToNotify(testUserId?: string): Promise<ReviewNotificationUser[]> {
        const users = await this._reviewNotificationsUserRepository.getUsersToNotify();
        if (testUserId) {
            return users.filter((user) => user.userId === testUserId);
        }

        const userIdsWithFeatureEnabled = await isFeatureAvailableForUsers({
            userIds: users.map((user) => user.userId),
            featureName: 'release-negative-reviews-email-notifications',
        });
        return users.filter((user) => userIdsWithFeatureEnabled.includes(user.userId));
    }

    private async _getNegativeReviewsToBeNotifiedGroupedByRestaurantId(): Promise<ReviewsGroupedByRestaurantId> {
        const reviews = await this._reviewsRepository.getNegativeReviewsToBeNotified();
        return groupBy(reviews, (r) => r.restaurantId.toString());
    }

    private _getAllRestaurantsReviewsForUser(
        restaurants: ReviewNotificationUser['restaurants'],
        reviewsGroupedByRestaurantId: ReviewsGroupedByRestaurantId
    ): IReview[] {
        return restaurants
            .map((restaurant) => reviewsGroupedByRestaurantId[restaurant.id])
            .flat()
            .filter(isNotNil);
    }

    private async _getNotificationForUser(
        notificationUser: ReviewNotificationUser,
        reviews: IReview[],
        channel: NotificationChannel
    ): Promise<NegativeReviewNotification | null> {
        const userNotifications = await this._notificationsRepository.getUserNegativeReviewsNotifications(notificationUser.userId, channel);
        const reviewsToNotify = this._filterNotNotifiedReviews(reviews, userNotifications);
        if (!reviewsToNotify.length) {
            return null;
        }
        const restaurantsGroupedByRestId = groupBy(notificationUser.restaurants, (r) => r.id.toString());
        const mainReviewToNotify = this._getMostRecentReview(reviewsToNotify);

        const mainRestaurantReview: NegativeReviewNotificationData['mainRestaurantReview'] = {
            id: mainReviewToNotify._id.toString(),
            text: mainReviewToNotify.text ?? undefined,
            platformKey: mainReviewToNotify.key,
            socialCreatedAt: mainReviewToNotify.socialCreatedAt,
            rating: mainReviewToNotify.rating ?? 0,
            reviewerName: mainReviewToNotify.reviewer?.displayName,
            reviewerProfilePictureUrl: mainReviewToNotify.reviewer?.profilePhotoUrl ?? undefined,
            socialAttachmentUrls:
                mainReviewToNotify.socialAttachments
                    ?.filter((attachment) => attachment.type === MediaType.PHOTO)
                    ?.map((attachment) => attachment.urls?.small || attachment.urls?.original || null)
                    ?.filter(isNotNil) || [],
        };

        const [mainRestaurantReviews, otherRestaurantReviews] = partition(
            reviewsToNotify,
            (review) => review.restaurantId.toString() === mainReviewToNotify.restaurantId.toString()
        );

        const mainRestaurantName = restaurantsGroupedByRestId[mainReviewToNotify.restaurantId.toString()][0].name;
        const mainRestaurantId = mainReviewToNotify.restaurantId.toString();
        const mainRestaurantReviewCount = mainRestaurantReviews.length;
        const otherRestaurantsReviewCount = otherRestaurantReviews.length;
        const otherRestaurantNames = otherRestaurantReviews.map(
            (review) => restaurantsGroupedByRestId[review.restaurantId.toString()][0].name
        );

        return new NegativeReviewNotification({
            userId: notificationUser.userId,
            data: {
                restaurantIds: uniq(reviewsToNotify.map((review) => review.restaurantId.toString())),
                mainRestaurantReview,
                mainRestaurantName,
                mainRestaurantId,
                mainRestaurantReviewCount,
                otherRestaurantsReviewCount,
                otherRestaurantNames: uniq(otherRestaurantNames),
            },
            type: NotificationType.REVIEW_REPLY_REMINDER,
            channel,
            user: notificationUser.user,
        });
    }

    private _filterNotNotifiedReviews(reviews: IReview[], userNotifications: NegativeReviewNotification[]): IReview[] {
        const mainNotificationReviews = userNotifications.map((notification) => notification.data.mainRestaurantReview).filter(isNotNil);
        return reviews.filter(
            (review) => !mainNotificationReviews.some((notificationReview) => notificationReview.id === review._id.toString())
        );
    }

    private _getMostRecentReview(reviews: IReview[]): IReview {
        return reviews.sort((a, b) => b.socialCreatedAt.getTime() - a.socialCreatedAt.getTime())[0];
    }
}
