import { DateTime } from 'luxon';
import { container } from 'tsyringe';
import { v4 as uuidV4 } from 'uuid';

import {
    CaslRole,
    NEGATIVE_REVIEW_MAX_DAYS_NOTIFICATION,
    NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION,
    NotificationChannel,
    NotificationType,
    PlatformKey,
    PlatformPresenceStatus,
} from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { NegativeReviewNotification } from ':modules/notifications/entities/negative-review-notification.entity';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import {
    getDefaultNegativeReviewNotificationData,
    getDefaultNotification,
    getNotificationReviewFromReview,
    getNotificationUserFromUser,
} from ':modules/notifications/tests/notification.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { defaultUserSettings, getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import * as experimentationModule from ':services/experimentations-service/experimentation.service';

import { CreateNegativeReviewsNotificationsUseCase } from './create-negative-reviews-notifications.use-case';

describe('CreateNegativeReviewNotificationsUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'NotificationsRepository',
            'ReviewsRepository',
            'UserRestaurantsRepository',
            'UsersRepository',
            'RestaurantsRepository',
        ]);

        const isFeatureAvailableForUsersSpy = jest.spyOn(experimentationModule, 'isFeatureAvailableForUsers');
        isFeatureAvailableForUsersSpy.mockImplementation(({ userIds }: { userIds: string[] }) => Promise.resolve(userIds));

        container.register<NotificationSenderService>(InjectionToken.NotificationSenderService, {
            useValue: {
                sendNotificationsToChannels: async (_) => void 0,
            },
        });
    });

    describe('execute', () => {
        it('should not create notifications if all restaurants are inactive', async () => {
            const createNegativeReviewNotificationsUseCase = container.resolve(CreateNegativeReviewsNotificationsUseCase);
            const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().name('restaurant_0').uniqueKey('restaurant_0_key').active(false).build(),
                                getDefaultRestaurant().name('restaurant_1').uniqueKey('restaurant_1_key').active(false).build(),
                            ];
                        },
                    },
                    users: {
                        data() {
                            return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .caslRole(CaslRole.ADMIN)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .caslRole(CaslRole.EDITOR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_dependencies): NegativeReviewNotification[] {
                    return [];
                },
            });

            await testCase.build();
            const exepectedResult = testCase.getExpectedResult();
            const result = await createNegativeReviewNotificationsUseCase.execute();
            expect(result).toIncludeSameMembers(exepectedResult);
        });

        it('should not create notifications if all users notification settings are inactive', async () => {
            const createNegativeReviewNotificationsUseCase = container.resolve(CreateNegativeReviewsNotificationsUseCase);
            const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().name('restaurant_0').uniqueKey('restaurant_0_key').active(true).build(),
                                getDefaultRestaurant().name('restaurant_1').uniqueKey('restaurant_1_key').active(true).build(),
                            ];
                        },
                    },
                    users: {
                        data() {
                            return [
                                getDefaultUser()
                                    .name('user_0')
                                    .email('<EMAIL>')
                                    .settings({
                                        ...defaultUserSettings,
                                        notifications: {
                                            ...defaultUserSettings.notifications,
                                            email: {
                                                ...defaultUserSettings.notifications.email,
                                                reviewReplyReminder: { active: false },
                                            },
                                            web: {
                                                ...defaultUserSettings.notifications.web,
                                                reviewReplyReminder: { active: false },
                                            },
                                            mobile: {
                                                ...defaultUserSettings.notifications.mobile,
                                                newReviews: { ...defaultUserSettings.notifications.mobile.newReviews, active: false },
                                            },
                                        },
                                    })
                                    .build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .caslRole(CaslRole.ADMIN)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .caslRole(CaslRole.EDITOR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_dependencies): NegativeReviewNotification[] {
                    return [];
                },
            });

            await testCase.build();
            const exepectedResult = testCase.getExpectedResult();
            const result = await createNegativeReviewNotificationsUseCase.execute();
            expect(result).toIncludeSameMembers(exepectedResult);
        });

        it('should not create notifications if there is no reviews to be notified', async () => {
            const createNegativeReviewNotificationsUseCase = container.resolve(CreateNegativeReviewsNotificationsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().name('restaurant_0').uniqueKey('restaurant_0_key').active(true).build()];
                        },
                    },
                    users: {
                        data() {
                            return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .caslRole(CaslRole.ADMIN)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                // review too recent
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION - 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                // review too old
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MAX_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                // review with comments
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                // positive review without comments
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(5)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                // too old ubereats review
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .key(PlatformKey.UBEREATS)
                                    .socialCreatedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                    .build(),

                                // foursquare review
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .key(PlatformKey.FOURSQUARE)
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                // theFork review without text
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text(null)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .key(PlatformKey.LAFOURCHETTE)
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                // resy review
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .key(PlatformKey.RESY)
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                // review with platformPresenceStatus NOT_FOUND
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .platformPresenceStatus(PlatformPresenceStatus.NOT_FOUND)
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_dependencies): NegativeReviewNotification[] {
                    return [];
                },
            });

            await testCase.build();
            const exepectedResult = testCase.getExpectedResult();
            const result = await createNegativeReviewNotificationsUseCase.execute();
            expect(result).toIncludeSameMembers(exepectedResult);
        });

        it('should not create notifications if all reviews are already notified', async () => {
            const createNegativeReviewNotificationsUseCase = container.resolve(CreateNegativeReviewsNotificationsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'reviews' | 'notifications'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().name('restaurant_0').uniqueKey('restaurant_0_key').active(true).build()];
                        },
                    },
                    users: {
                        data() {
                            return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .caslRole(CaslRole.ADMIN)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),

                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MAX_DAYS_NOTIFICATION - 1 })
                                            .toJSDate()
                                    )
                                    .build(),
                            ];
                        },
                    },
                    notifications: {
                        data(dependencies) {
                            return [
                                getDefaultNotification()
                                    .restaurantId(undefined)
                                    .channel(NotificationChannel.EMAIL)
                                    .type(NotificationType.REVIEW_REPLY_REMINDER)
                                    .userId(dependencies.users()[0]._id)
                                    .data(getDefaultNegativeReviewNotificationData({ review: dependencies.reviews()[0] }).build())
                                    .build(),

                                getDefaultNotification()
                                    .restaurantId(undefined)
                                    .channel(NotificationChannel.EMAIL)
                                    .type(NotificationType.REVIEW_REPLY_REMINDER)
                                    .userId(dependencies.users()[0]._id)
                                    .data(getDefaultNegativeReviewNotificationData({ review: dependencies.reviews()[1] }).build())
                                    .build(),
                                getDefaultNotification()
                                    .restaurantId(undefined)
                                    .channel(NotificationChannel.WEB)
                                    .type(NotificationType.REVIEW_REPLY_REMINDER)
                                    .userId(dependencies.users()[0]._id)
                                    .data(getDefaultNegativeReviewNotificationData({ review: dependencies.reviews()[0] }).build())
                                    .build(),

                                getDefaultNotification()
                                    .restaurantId(undefined)
                                    .channel(NotificationChannel.WEB)
                                    .type(NotificationType.REVIEW_REPLY_REMINDER)
                                    .userId(dependencies.users()[0]._id)
                                    .data(getDefaultNegativeReviewNotificationData({ review: dependencies.reviews()[1] }).build())
                                    .build(),
                                getDefaultNotification()
                                    .restaurantId(undefined)
                                    .channel(NotificationChannel.MOBILE)
                                    .type(NotificationType.REVIEW_REPLY_REMINDER)
                                    .userId(dependencies.users()[0]._id)
                                    .data(getDefaultNegativeReviewNotificationData({ review: dependencies.reviews()[0] }).build())
                                    .build(),

                                getDefaultNotification()
                                    .restaurantId(undefined)
                                    .channel(NotificationChannel.MOBILE)
                                    .type(NotificationType.REVIEW_REPLY_REMINDER)
                                    .userId(dependencies.users()[0]._id)
                                    .data(getDefaultNegativeReviewNotificationData({ review: dependencies.reviews()[1] }).build())
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_dependencies): NegativeReviewNotification[] {
                    return [];
                },
            });

            await testCase.build();
            const exepectedResult = testCase.getExpectedResult();
            const result = await createNegativeReviewNotificationsUseCase.execute();
            expect(result).toIncludeSameMembers(exepectedResult);
        });

        it('should create notifications for users', async () => {
            const createNegativeReviewNotificationsUseCase = container.resolve(CreateNegativeReviewsNotificationsUseCase);
            const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().name('restaurant_0').uniqueKey('restaurant_0_key').active(true).build(),
                                getDefaultRestaurant().name('restaurant_1').uniqueKey('restaurant_1_key').active(true).build(),
                                getDefaultRestaurant().name('restaurant_2').uniqueKey('restaurant_2_key').active(true).build(),
                                getDefaultRestaurant().name('restaurant_3').uniqueKey('restaurant_3_key').active(false).build(),
                                getDefaultRestaurant().name('restaurant_4').uniqueKey('restaurant_4_key').active(true).build(),
                                getDefaultRestaurant().name('restaurant_5').uniqueKey('restaurant_5_key').active(true).build(),
                                getDefaultRestaurant().name('restaurant_6').uniqueKey('restaurant_6_key').active(true).build(),
                            ];
                        },
                    },
                    users: {
                        data() {
                            return [
                                getDefaultUser().name('user_0').email('<EMAIL>').build(),
                                getDefaultUser().name('user_1').email('<EMAIL>').build(),
                                getDefaultUser()
                                    .name('user_2')
                                    .email('<EMAIL>')
                                    .settings({
                                        ...defaultUserSettings,
                                        notifications: {
                                            ...defaultUserSettings.notifications,
                                            email: {
                                                ...defaultUserSettings.notifications.email,
                                                reviewReplyReminder: { active: false },
                                            },
                                            web: {
                                                ...defaultUserSettings.notifications.web,
                                                reviewReplyReminder: { active: false },
                                            },
                                            mobile: {
                                                ...defaultUserSettings.notifications.mobile,
                                                newReviews: { ...defaultUserSettings.notifications.mobile.newReviews, active: false },
                                            },
                                        },
                                    })
                                    .build(),

                                getDefaultUser()
                                    .name('user_3')
                                    .settings({
                                        ...defaultUserSettings,
                                        notifications: {
                                            ...defaultUserSettings.notifications,
                                            mobile: {
                                                ...defaultUserSettings.notifications.mobile,
                                                newReviews: { ...defaultUserSettings.notifications.mobile.newReviews, realtime: false },
                                            },
                                        },
                                    })
                                    .email('<EMAIL>')
                                    .build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .caslRole(CaslRole.ADMIN)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .caslRole(CaslRole.EDITOR)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[4]._id)
                                    .caslRole(CaslRole.GUEST)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[5]._id)
                                    .caslRole(CaslRole.ADMIN)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[0]._id)
                                    .restaurantId(dependencies.restaurants()[6]._id)
                                    .caslRole(CaslRole.OWNER)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[1]._id)
                                    .restaurantId(dependencies.restaurants()[4]._id)
                                    .caslRole(CaslRole.OWNER)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[1]._id)
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .caslRole(CaslRole.GUEST)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[1]._id)
                                    .restaurantId(dependencies.restaurants()[3]._id)
                                    .caslRole(CaslRole.ADMIN)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[2]._id)
                                    .restaurantId(dependencies.restaurants()[5]._id)
                                    .caslRole(CaslRole.EDITOR)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[2]._id)
                                    .restaurantId(dependencies.restaurants()[3]._id)
                                    .caslRole(CaslRole.MODERATOR)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[3]._id)
                                    .restaurantId(dependencies.restaurants()[5]._id)
                                    .caslRole(CaslRole.OWNER)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[3]._id)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .caslRole(CaslRole.GUEST)
                                    .build(),

                                getDefaultUserRestaurant()
                                    .userId(dependencies.users()[3]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .caslRole(CaslRole.EDITOR)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                // 0
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 4 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 1
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(uuidV4())
                                    .rating(5)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 2
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 2 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 3
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .socialId(uuidV4())
                                    .rating(1)
                                    .comments([])
                                    .socialCreatedAt(DateTime.now().toJSDate())
                                    .build(),
                                // 4
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 3 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 5
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 2 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 6
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 2 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 7
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[3]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 8
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[4]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 2 })
                                            .toJSDate()
                                    )
                                    .build(),
                                // 9
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[5]._id)
                                    .socialId(uuidV4())
                                    .rating(2)
                                    .comments([])
                                    .socialCreatedAt(
                                        DateTime.now()
                                            .minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 })
                                            .toJSDate()
                                    )
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): NegativeReviewNotification[] {
                    const notification1 = {
                        id: expect.any(String),
                        userId: dependencies.users[0]._id.toString(),
                        data: {
                            restaurantIds: [
                                dependencies.restaurants[5]._id.toString(),
                                dependencies.restaurants[2]._id.toString(),
                                dependencies.restaurants[1]._id.toString(),
                            ],
                            mainRestaurantReview: getNotificationReviewFromReview({ review: dependencies.reviews[9] }),
                            mainRestaurantName: dependencies.restaurants[5].name,
                            mainRestaurantId: dependencies.restaurants[5]._id.toString(),
                            mainRestaurantReviewCount: 1,
                            otherRestaurantsReviewCount: 4,
                            otherRestaurantNames: [dependencies.restaurants[2].name, dependencies.restaurants[1].name],
                        },
                        type: NotificationType.REVIEW_REPLY_REMINDER,
                        createdAt: expect.any(Date),
                        updatedAt: expect.any(Date),
                        completedAt: null,
                        error: null,
                        readAt: null,
                        user: getNotificationUserFromUser(dependencies.users[0]),
                    };

                    const notification2 = {
                        id: expect.any(String),
                        userId: dependencies.users[1]._id.toString(),
                        data: {
                            restaurantIds: [dependencies.restaurants[4]._id.toString()],
                            mainRestaurantReview: getNotificationReviewFromReview({ review: dependencies.reviews[8] }),
                            mainRestaurantName: dependencies.restaurants[4].name,
                            mainRestaurantId: dependencies.restaurants[4]._id.toString(),
                            mainRestaurantReviewCount: 1,
                            otherRestaurantsReviewCount: 0,
                            otherRestaurantNames: [],
                        },
                        type: NotificationType.REVIEW_REPLY_REMINDER,
                        channel: NotificationChannel.EMAIL,
                        createdAt: expect.any(Date),
                        updatedAt: expect.any(Date),
                        completedAt: null,
                        error: null,
                        readAt: null,
                        user: getNotificationUserFromUser(dependencies.users[1]),
                    };

                    const notification3 = {
                        id: expect.any(String),
                        userId: dependencies.users[3]._id.toString(),
                        data: {
                            restaurantIds: [dependencies.restaurants[5]._id.toString(), dependencies.restaurants[0]._id.toString()],
                            mainRestaurantReview: getNotificationReviewFromReview({ review: dependencies.reviews[9] }),
                            mainRestaurantName: dependencies.restaurants[5].name,
                            mainRestaurantId: dependencies.restaurants[5]._id.toString(),
                            mainRestaurantReviewCount: 1,
                            otherRestaurantsReviewCount: 1,
                            otherRestaurantNames: [dependencies.restaurants[0].name],
                        },
                        type: NotificationType.REVIEW_REPLY_REMINDER,
                        channel: NotificationChannel.EMAIL,
                        createdAt: expect.any(Date),
                        updatedAt: expect.any(Date),
                        completedAt: null,
                        error: null,
                        readAt: null,
                        user: getNotificationUserFromUser(dependencies.users[3]),
                    };

                    return [
                        new NegativeReviewNotification({ ...notification1, channel: NotificationChannel.EMAIL }),
                        new NegativeReviewNotification({ ...notification1, channel: NotificationChannel.WEB }),
                        new NegativeReviewNotification({ ...notification2, channel: NotificationChannel.EMAIL }),
                        new NegativeReviewNotification({ ...notification2, channel: NotificationChannel.WEB }),
                        new NegativeReviewNotification({ ...notification3, channel: NotificationChannel.EMAIL }),
                        new NegativeReviewNotification({ ...notification3, channel: NotificationChannel.WEB }),
                        new NegativeReviewNotification({ ...notification3, channel: NotificationChannel.MOBILE }),
                    ];
                },
            });

            await testCase.build();
            const exepectedResult: NegativeReviewNotification[] = testCase.getExpectedResult();
            const results = await createNegativeReviewNotificationsUseCase.execute();

            const sortedResults = results.map((result) => ({
                ...result,
                data: {
                    ...result.data,
                    restaurantIds: result.data.restaurantIds.sort(),
                    otherRestaurantNames: result.data.otherRestaurantNames.sort(),
                },
            }));

            const sortedExpectedResults = exepectedResult.map((result) => ({
                ...result,
                data: {
                    ...result.data,
                    restaurantIds: result.data.restaurantIds.sort(),
                    otherRestaurantNames: result.data.otherRestaurantNames.sort(),
                },
            }));

            expect(sortedResults).toIncludeSameMembers(sortedExpectedResults);
        });
    });
});
