import { EntityConstructor } from '@malou-io/package-utils';

import { NotificationUser, NotificationUserProps } from './notification-user.entity';

type ReviewNotificationUserProps = EntityConstructor<ReviewNotificationUser, { user: NotificationUserProps }>;

export class ReviewNotificationUser {
    userId: string;
    user: NotificationUser;
    restaurants: {
        id: string;
        name: string;
    }[];

    constructor(props: ReviewNotificationUserProps) {
        this.userId = props.userId;
        this.user = new NotificationUser(props.user);
        this.restaurants = props.restaurants;
    }
}
