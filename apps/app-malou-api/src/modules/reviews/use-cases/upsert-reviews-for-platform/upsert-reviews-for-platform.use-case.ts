import { JobAttributesData } from 'agenda';
import { chunk, differenceBy, isNil } from 'lodash';
import { DateTime } from 'luxon';
import { delay, inject, singleton } from 'tsyringe';

import { DbId, ID, IPlatform, IReview, IReviewComment, toDbId } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    DEFAULT_LANG_UNKNOWN,
    EmailCategory,
    EmailType,
    errorReplacer,
    filterByRequiredKeys,
    getPlatformDefinition,
    isFulfilled,
    isNotNil,
    MalouErrorCode,
    MIN_POSITIVE_REVIEW_RATING,
    PlatformDataFetchedStatus,
    PlatformKey,
    PlatformPresenceStatus,
    PostedStatus,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { isRejected } from ':helpers/utils';
import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { IntelligentSubjectsDetectionService } from ':modules/ai/services/intelligent-subjects-detection/intelligent-subjects-detection.service';
import { AutoReplyUseCases } from ':modules/automations/auto-reply.use-cases';
import { EmailData } from ':modules/mailing/interface';
import MailingUseCases from ':modules/mailing/use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReview, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { GetPlatformReviewsService } from ':modules/reviews/services/get-review-platform-use-cases';
import { StartPreviousReviewsAnalysisService } from ':modules/reviews/services/start-previous-reviews-analysis/start-previous-reviews-analysis.service';
import { DeleteReviewSemanticAnalysisService } from ':modules/segment-analyses/services/delete-review-semantic-analysis.service';
import { StartReviewSemanticAnalysisService } from ':modules/segment-analyses/services/start-review-semantic-analysis.service';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';
import { metricsService } from ':services/metrics.service';
import { SlackChannel, SlackService } from ':services/slack.service';
import { GenerateLanguageDetectionService } from ':services/text-translator/generate-language-detection.service';

const upsertReviewForPlatformDurationHistogram = metricsService.getMeter().createHistogram<{
    platform: PlatformKey;
}>('reviews.upsertReviewForPlatformDuration', {
    description: 'Duration of the function upsertReviewsForPlatform (which inserts reviews in bulk)',
    advice: { explicitBucketBoundaries: [1, 10, 100] },
    unit: 'seconds',
});

const upsertedReviewsCounter = metricsService.getMeter().createCounter<{
    platform: PlatformKey;
}>('reviews.upsertedReviewsCounter', { description: 'Counts upserted reviews (individually)' });

type UpsertReviewsForPlatformUseCaseReviewParam =
    | { sentToSQS: boolean; socialId: string }
    | { error: boolean; message?: string; errorData?: any }
    | PlatformReview[];

@singleton()
export class UpsertReviewsForPlatformUseCase {
    _MAX_KEYWORD_ANALYSIS_COUNT = 1500;

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        @inject(delay(() => MailingUseCases))
        private readonly _mailingUseCases: MailingUseCases,
        private readonly _autoReplyUseCases: AutoReplyUseCases,
        private readonly _startReviewSemanticAnalysisService: StartReviewSemanticAnalysisService,
        private readonly _startPreviousReviewsAnalysisService: StartPreviousReviewsAnalysisService,
        private readonly _deleteReviewSemanticAnalysisService: DeleteReviewSemanticAnalysisService,
        private readonly _generateLanguageDetectionService: GenerateLanguageDetectionService,
        private readonly _slackService: SlackService,
        private readonly _generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService,
        private readonly _intelligentSubjectsDetectionService: IntelligentSubjectsDetectionService,
        private readonly _getPlatformReviewsService: GetPlatformReviewsService
    ) {}

    async execute(reviews: UpsertReviewsForPlatformUseCaseReviewParam, platformKey: PlatformKey, restaurantId: DbId): Promise<any> {
        const platform = await this._platformsRepository.findOneOrFail({
            filter: {
                restaurantId,
                key: platformKey,
            },
            options: { lean: true },
        });

        try {
            // If upserting has been sent to SQS, update the restaurant state and return
            if (this._isSentToSQS(reviews)) {
                return;
            }

            if (this._isError(reviews)) {
                throw new MalouError(MalouErrorCode.REVIEWS_UPDATE_ERROR, {
                    metadata: {
                        rawData: reviews?.message,
                    },
                    message: reviews?.errorData ?? reviews?.message,
                });
            }

            if (Array.isArray(reviews)) {
                const includesRestaurantFirstBadReview = await this._includesRestaurantFirstBadReview(reviews, platform);
                if (includesRestaurantFirstBadReview) {
                    await this._sendMailToDownloadMobileApp(restaurantId);
                }
            }

            await this._upsertReviewsForPlatform(reviews, platform);
            return this._restaurantsRepository.findOneAndUpdate({
                filter: {
                    _id: restaurantId,
                },
                update: {
                    [`currentState.reviews.fetched.${platform.key}`]: {
                        status: PlatformDataFetchedStatus.SUCCESS,
                        lastTried: new Date(),
                        error: null,
                    },
                },
            });
        } catch (error) {
            logger.error('[REVIEWS_UPDATE_ERROR] - Error trying to upsert reviews', {
                platformId: platform._id,
                restaurantId: platform.restaurantId,
                error,
            });
            return this._restaurantsRepository.findOneAndUpdate({
                filter: {
                    _id: restaurantId,
                },
                update: {
                    [`currentState.reviews.fetched.${platform.key}`]: {
                        status: PlatformDataFetchedStatus.ERROR,
                        lastTried: new Date(),
                        error: this._clarifyError(error),
                    },
                },
            });
        }
    }

    private _isSentToSQS(reviews: UpsertReviewsForPlatformUseCaseReviewParam): reviews is { sentToSQS: boolean; socialId: string } {
        return (reviews as { sentToSQS: boolean }).sentToSQS !== undefined;
    }

    private _isError(
        reviews: UpsertReviewsForPlatformUseCaseReviewParam
    ): reviews is { error: boolean; message?: string; errorData?: any } {
        return (reviews as { error: boolean }).error !== undefined;
    }

    private async _includesRestaurantFirstBadReview(reviews: PlatformReview[], platform: IPlatform): Promise<boolean> {
        const { restaurantId } = platform;
        const mappedReviews = this._getPlatformReviewsService.execute(platform.key).mapReviewsDataToMalou(platform, reviews);
        const badReview = await this._reviewsRepository.findOne({
            filter: { restaurantId, rating: { $lte: MIN_POSITIVE_REVIEW_RATING } },
            options: { lean: true },
        });

        return !badReview && filterByRequiredKeys(mappedReviews, ['rating']).some((review) => review.rating <= 3);
    }

    private async _userHasNoRestaurantWithBadReview(userId: string): Promise<boolean> {
        const userRestaurants = await this._userRestaurantsRepository.find({ filter: { userId } });
        const restaurantIds = userRestaurants.map((userRestaurant) => userRestaurant.restaurantId);
        const badReview = await this._reviewsRepository.findOne({ filter: { restaurantId: { $in: restaurantIds }, rating: { $lte: 3 } } });

        return !badReview;
    }

    private async _sendMailToDownloadMobileApp(restaurantId: DbId): Promise<void> {
        // filter users without email and admins
        const users = await this._userRestaurantsRepository.getRestaurantUsers(restaurantId.toString());
        const usersFiltered = users.filter((user) => user.email && !user.email.includes('@malou.io'));

        for (const user of usersFiltered) {
            // check that user doesn't have a restaurant with a bad review yet
            const userHasNoRestaurantWithBadReview = await this._userHasNoRestaurantWithBadReview(user.id);
            if (userHasNoRestaurantWithBadReview) {
                const emailData: EmailData = {
                    user: {
                        _id: toDbId(user.id),
                        email: user.email,
                        defaultLanguage: user.defaultLanguage,
                        lastname: user.lastname,
                        name: user.name,
                    },
                };
                await this._mailingUseCases.sendEmail(EmailCategory.MOBILE_APP_NOTIFICATION, EmailType.DOWNLOAD_MOBILE_APP, emailData);
            }
        }
    }

    private async _upsertReviewsForPlatform(platformReviews: PlatformReview[], platform: IPlatform): Promise<IReview[]> {
        const { restaurantId } = platform;
        platformReviews = platformReviews ?? [];
        if (platformReviews.length === 0 && platform.key !== PlatformKey.FACEBOOK) {
            return [];
        }

        const beginTimestamp = +new Date();
        try {
            const mappedReviews = this._getPlatformReviewsService.execute(platform.key).mapReviewsDataToMalou(platform, platformReviews);
            const reviews = await this._keepMalouPropertiesForExistingComments(mappedReviews, platform._id?.toString());

            // mutates reviews!
            await this._appendDraftCommentsToFetchedReviews(reviews, platform._id);

            const shouldDeleteReviewsBeforeUpsert = getPlatformDefinition(platform.key)?.shouldDeleteReviewsBeforeUpsert;
            // need to be in line with the platforms and some reviews might be deleted
            if (shouldDeleteReviewsBeforeUpsert) {
                const sortingField = this._getSortingFieldByPlatformKey(platform.key);
                const oldestReviewFetched = mappedReviews.reduce(
                    (prev, current) => (prev?.[sortingField] < current?.[sortingField] ? prev : current),
                    {}
                );
                const reviewsSinceOldestFetched = await this._reviewsRepository.find({
                    filter: {
                        restaurantId,
                        platformId: platform._id,
                        [sortingField]: {
                            $gte: oldestReviewFetched?.[sortingField] ?? new Date(0),
                        },
                    },
                    projection: { socialId: 1, platformId: 1 },
                    options: { lean: true },
                });
                const disappearedReviews = differenceBy(reviewsSinceOldestFetched, mappedReviews, 'socialId');
                await Promise.allSettled(
                    disappearedReviews.map(({ socialId, platformId }) =>
                        this._reviewsRepository.findOneAndUpdate({
                            filter: { socialId, platformId },
                            update: { platformPresenceStatus: PlatformPresenceStatus.NOT_FOUND },
                        })
                    )
                );
                if (disappearedReviews.length) {
                    logger.warn('[UPSERT_REVIEWS] Flagging reviews as disappeared', {
                        reviewsCount: disappearedReviews.length,
                        platformId: platform._id,
                        platformKey: platform.key,
                        restaurantId: platform.restaurantId,
                        reviewSocialIds: disappearedReviews?.map((r) => r.socialId),
                    });
                }
            }

            logger.info('[UPSERT_REVIEWS] Upserting reviews', {
                reviewsCount: reviews.length,
                platformId: platform._id,
                platformKey: platform.key,
                restaurantId: platform.restaurantId,
                reviewSocialIds: reviews.map((r) => r.socialId),
            });

            if (platform.key === PlatformKey.UBEREATS) {
                const ubereatsReviews = await this._getReviewsForUbereatsPromotionAmountCheck(reviews, platform._id);
                this._checkUbereatsPromotionAmount(ubereatsReviews, reviews).catch((err) =>
                    logger.error('[UPSERT_REVIEWS] Error when checking Ubereats promotion value', { err })
                );
            }

            // Upsert them by batch of 100 to avoid OOM errors
            const reviewsWithKeywordAnalysisChunks = chunk(reviews, 100);
            const results: PromiseSettledResult<IReview>[] = [];
            for (const reviewsWithKeywordAnalysisChunk of reviewsWithKeywordAnalysisChunks) {
                const reviewsWithKeywordAnalysisChunkResults = await Promise.allSettled(
                    reviewsWithKeywordAnalysisChunk.map((review) =>
                        this._reviewsRepository.upsert({
                            filter: { socialId: review.socialId, platformId: review.platformId },
                            update: {
                                aiRelevantBricks: null, // Explicitly set to null before destructuring review data to avoid having [] as default value
                                ...review,
                                socialSortDate: review.socialUpdatedAt ?? review.socialCreatedAt,
                                platformPresenceStatus: PlatformPresenceStatus.FOUND,
                            },
                            options: { lean: true },
                        })
                    )
                );

                results.push(...reviewsWithKeywordAnalysisChunkResults);
            }

            upsertedReviewsCounter.add(reviews.length, { platform: platform.key });
            const errors = results.filter(isRejected);
            if (errors.length) {
                logger.error('[REVIEWS_UPDATE_ERROR]', errors.map((err) => err.reason).join('\n'));
            }
            const upsertedReviews = results.filter(isFulfilled).map((res: PromiseFulfilledResult<IReview>) => res.value);
            const upsertedReviewsWithLang = await this._detectLangAndUpdateReviews(upsertedReviews, platform.key);
            const updatedReviewsWithKeywordAnalysis = await this._getAllKeywordAnalysisForComments(
                upsertedReviewsWithLang,
                restaurantId.toString()
            );

            await this._intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews: updatedReviewsWithKeywordAnalysis,
            });

            const isNewSemanticAnalysisFeatureEnabledForRestaurant = await isFeatureAvailableForRestaurant({
                restaurantId: platform.restaurantId.toString(),
                featureName: 'release-new-semantic-analysis',
            });
            if (isNewSemanticAnalysisFeatureEnabledForRestaurant) {
                await this._handleStartMultipleSemanticAnalysis(updatedReviewsWithKeywordAnalysis);
            }
            await this._handleStartPreviousReviewsAnalysis(updatedReviewsWithKeywordAnalysis);
            await Promise.all(this._handleReviewsAutoReply(updatedReviewsWithKeywordAnalysis));
            return updatedReviewsWithKeywordAnalysis;
        } finally {
            upsertReviewForPlatformDurationHistogram.record((+new Date() - beginTimestamp) / 1000, { platform: platform.key });
        }
    }

    private async _keepMalouPropertiesForExistingComments(
        reviews: ReviewInputWithRestaurantAndPlatformIds[],
        platformId: string
    ): Promise<IReview[]> {
        const updatedReviews: IReview[] = [];
        for (const review of reviews) {
            const reviewFromDb = await this._reviewsRepository.findOne({
                filter: {
                    socialId: review.socialId,
                    platformId,
                },
                projection: {
                    comments: 1,
                    text: 1,
                    aiRelatedBricksCount: 1,
                    aiRelevantBricks: 1,
                    lang: 1,
                    responseStyle: 1,
                    matchedReviewsIds: 1,
                    reviewerNameValidation: 1,
                },
            });
            if (review.comments?.length) {
                const commentsWithKeywordAnalysis: any[] = [];
                for (const comment of review.comments) {
                    const commentFromDb = reviewFromDb?.comments?.find((c) => c.text === comment.text);
                    commentsWithKeywordAnalysis.push({
                        ...comment,
                        keywordAnalysis: commentFromDb?.keywordAnalysis ?? undefined,
                        templateIdUsed: commentFromDb?.templateIdUsed,
                        isRepliedFromAggregatedView: commentFromDb?.isRepliedFromAggregatedView,
                        aiInteractionIdUsed: commentFromDb?.aiInteractionIdUsed,
                        isMalou: commentFromDb?.isMalou,
                        author: commentFromDb?.author,
                    });
                }
                review.comments = commentsWithKeywordAnalysis;
            }
            if (review.text && reviewFromDb?.text !== review.text) {
                review.semanticAnalysisFetchStatus = undefined;
                await this._removePreviousSemanticAnalysis({
                    socialId: review.socialId,
                    platformKey: review.key,
                });
            }
            review.aiRelatedBricksCount = reviewFromDb?.aiRelatedBricksCount ?? null;
            review.aiRelevantBricks = reviewFromDb?.aiRelevantBricks ?? null;
            review.lang = reviewFromDb?.lang ?? null;
            review.responseStyle = reviewFromDb?.responseStyle ?? undefined;
            review.matchedReviewsIds = reviewFromDb?.matchedReviewsIds?.map((id) => id.toString());
            review.reviewerNameValidation = reviewFromDb?.reviewerNameValidation ?? undefined;
            updatedReviews.push(review as any);
        }
        return updatedReviews;
    }

    /**
     * Some review replies are not published yet. Some of these unpublished replies are
     * stored in our database (with the `posted` field set to something different than
     * PostedStatus.POSTED) but are unknown by the platform. The parameter `fetchedReviews`
     * is supposed to be a list of reviews fetched from the platform
     * and this function appends missing draft comments to them.
     *
     * This function mutates its input.
     */
    private async _appendDraftCommentsToFetchedReviews(fetchedReviews: IReview[], platformId: ID): Promise<void> {
        // in practice very few reviews are fetched here because very few comments
        // are not posted.
        const dbReviews: IReview[] = await this._reviewsRepository.find({
            filter: {
                platformId,
                comments: { $elemMatch: { posted: { $ne: PostedStatus.POSTED } } },
            },
            options: { lean: true },
        });

        for (const fetchedReview of fetchedReviews) {
            const dbReview = dbReviews.find((r) => r.socialId === fetchedReview.socialId);
            if (!dbReview) {
                continue;
            }
            const dbComments: IReviewComment[] = dbReview.comments ?? [];
            const fetchedCommentTexts = fetchedReview.comments.map((comment) => comment.text);
            const dbCommentToKeep = dbComments.find((comment) => !fetchedCommentTexts.includes(comment.text));
            if (dbCommentToKeep !== undefined) {
                fetchedReview.comments.push(dbCommentToKeep);
            }
        }
    }

    private _clarifyError(e: any): string {
        return e?.error_user_title || e?.error?.message || e?.message || e?.malouErrorCode || JSON.stringify(e, errorReplacer) || 'unknown';
    }

    private _getSortingFieldByPlatformKey(platformKey: string): string {
        switch (platformKey) {
            case PlatformKey.GMB:
                return 'socialUpdatedAt';
            default:
                return 'socialCreatedAt';
        }
    }

    private _handleReviewsAutoReply = (reviews: IReview[]): Promise<void | JobAttributesData>[] => {
        return reviews.map((review) =>
            this._autoReplyUseCases
                .handleReviewAutoReply(review)
                .catch((err) => logger.warn('[ReviewsUseCases] - [_handleReviewsAutoReply] error', err))
        );
    };

    private _removePreviousSemanticAnalysis = ({ socialId, platformKey }: { socialId: string; platformKey: PlatformKey }): Promise<void> =>
        this._deleteReviewSemanticAnalysisService
            .execute({ socialId, platformKey })
            .catch((e) => logger.error('[ReviewsUseCases] - [_removePreviousSemanticAnalysis] Error', e));

    private _handleStartMultipleSemanticAnalysis = (reviews: IReview[]): Promise<void[]> =>
        Promise.all(
            reviews
                .filter((r) => !r.semanticAnalysisFetchStatus)
                .sort((rA, rB) => new Date(rA.socialCreatedAt).getTime() - new Date(rB.socialCreatedAt).getTime())
                .map((review) =>
                    this._startReviewSemanticAnalysisService.execute({ review }).catch((e) => {
                        logger.error('[ReviewsUseCases] - [_handleFetchMultipleSemanticAnalysis] Error', e);
                    })
                )
        );

    private _handleStartPreviousReviewsAnalysis = (reviews: IReview[]): Promise<void[]> =>
        Promise.all(
            reviews
                .filter((review) => !review.reviewerNameValidation && !review.responseStyle)
                .sort((rA, rB) => new Date(rA.socialCreatedAt).getTime() - new Date(rB.socialCreatedAt).getTime())
                .map((review) =>
                    this._startPreviousReviewsAnalysisService.execute({ review }).catch((e) => {
                        logger.error('[ReviewsUseCases] - [_handleStartPreviousReviewsAnalysis] Error', e);
                    })
                )
        );

    private _getAllKeywordAnalysisForComments = async (reviews: IReview[], restaurantId: string): Promise<IReview[]> => {
        const oneYearAgo = DateTime.now().minus({ years: 1 });
        const reviewsWithMissingKeywordAnalysis = reviews.filter(
            (review) =>
                !!review.comments?.length &&
                DateTime.fromJSDate(new Date(review.socialCreatedAt)) > oneYearAgo &&
                review.comments.some((comment) => isNil(comment.keywordAnalysis?.score))
        );

        const acceptedReviewsWithMissingKeywordAnalysis = reviewsWithMissingKeywordAnalysis.slice(0, this._MAX_KEYWORD_ANALYSIS_COUNT);
        const updatedReviews = await Promise.all(
            acceptedReviewsWithMissingKeywordAnalysis.map((review) => this._getKeywordAnalysisForComments(review, restaurantId))
        );
        const updatedReviewIds = updatedReviews.map((review) => review?._id.toString());

        const otherReviews = reviews.filter((review) => !updatedReviewIds.includes(review._id.toString()));
        return [...updatedReviews, ...otherReviews] as any;
    };

    private _getKeywordAnalysisForComments = async (review: IReview, restaurantId: string) => {
        let wasUpdated = false;
        const updatedComments = await Promise.all(
            review?.comments.map(async (comment) => {
                if (isNotNil(comment.keywordAnalysis?.score) || !comment.text) {
                    return comment;
                }
                wasUpdated = true;
                const keywordAnalysis = await this._generateKeywordAnalysisForCommentService.execute({
                    reviewId: review._id.toString(),
                    restaurantId: toDbId(restaurantId),
                    reviewText: review.text ?? '',
                    rating: review.rating ?? null,
                    text: comment.text,
                    reviewLang: review.lang ?? undefined,
                    reviewSocialCreatedAt: review.socialCreatedAt,
                    reviewerName: review.reviewer?.displayName ?? '',
                    commentSocialUpdatedAt: comment.socialUpdatedAt,
                    reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
                });
                comment.keywordAnalysis = keywordAnalysis;
                return comment;
            })
        );
        if (!wasUpdated) {
            return review;
        }
        const updatedReview = await this._reviewsRepository.findOneAndUpdate({
            filter: { _id: review._id },
            update: {
                comments: updatedComments,
            },
            options: { lean: true },
        });
        return updatedReview;
    };

    private _detectLangAndUpdateReviews = async (reviews: IReview[] | undefined, platformKey: PlatformKey): Promise<IReview[]> => {
        if (!reviews) {
            return [];
        }

        if (!getPlatformDefinition(platformKey)?.shouldDetectReviewsLang) {
            return reviews ?? [];
        }
        if (!reviews?.length) {
            return reviews ?? [];
        }
        const reviewsWithText = reviews.filter((review) => !!review.text?.length && (!review.lang || review.lang === 'undetermined'));
        const reviewsWithTextIds = reviewsWithText.map((review) => review._id.toString());
        const promises = reviewsWithText.map(async (review) => {
            const lang = await this._detectLangForReview(review);
            return this._reviewsRepository.findOneAndUpdate({
                filter: { socialId: review.socialId, platformId: review.platformId },
                update: { lang },
            });
        });

        const updatedReviewsWithText = await Promise.all(promises);
        return [...updatedReviewsWithText, ...reviews.filter((review) => !reviewsWithTextIds.includes(review._id.toString()))] as any[];
    };

    private _detectLangForReview = async (review: IReview): Promise<string> => {
        try {
            if (review.lang) {
                return review.lang;
            }
            const detectedLang = await this._generateLanguageDetectionService.execute({
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                relatedEntityId: review._id.toString(),
                restaurantId: review.restaurantId.toString(),
                text: review.text ?? '',
            });
            return detectedLang;
        } catch (err) {
            return DEFAULT_LANG_UNKNOWN;
        }
    };

    private async _getReviewsForUbereatsPromotionAmountCheck(reviewsToUpsert: IReview[], platformId: DbId): Promise<IReview[]> {
        const socialIds = reviewsToUpsert.map((reviewToUpsert) => reviewToUpsert.socialId);
        const filter = {
            platformId,
            socialId: { $in: socialIds },
            comments: { $elemMatch: { ubereatsPromotionAmountInHundredths: { $ne: null } } },
        };
        return this._reviewsRepository.find({ filter, options: { lean: true } });
    }

    private async _checkUbereatsPromotionAmount(dbReviews: IReview[], reviewsToUpsert: IReview[]): Promise<void> {
        for (const dbReview of dbReviews) {
            const dbAmount = dbReview.comments?.[0]?.ubereatsPromotionAmountInHundredths;
            const toUpsertReview = reviewsToUpsert.find(
                (r) => r.socialId === dbReview.socialId && r.platformId.toString() === dbReview.platformId.toString()
            );
            const toUpsertAmount = toUpsertReview?.comments?.[0]?.ubereatsPromotionAmountInHundredths;
            if (dbAmount && toUpsertAmount && dbAmount !== toUpsertAmount) {
                this._slackService.sendAlert({
                    channel: SlackChannel.REVIEWS_ALERTS,
                    data: {
                        err: new Error(
                            `The Ubereats promotion amount fetched (${toUpsertAmount}) of review
                            ${dbReview.socialId} is not the same as the one computed (${dbAmount})`
                        ),
                    },
                });
            }
        }
    }
}
