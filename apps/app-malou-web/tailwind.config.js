/* eslint-disable @typescript-eslint/naming-convention */
/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ['./src/**/*.{html,ts}'],
    theme: {
        screens: {
            xxs: { max: '370px' },
            sm: { max: '639px' },
            md: { max: '767px' },
            lg: { max: '1023px' },
            xl: { max: '1279px' },
            '2xl': { max: '1535px' },
            '3xl': { max: '1919px' },
            '4xl': { max: '2559px' },
            'min-xxs': { min: '370px' },
            'min-sm': { min: '639px' },
            'min-md': { min: '767px' },
            'min-lg': { min: '1023px' },
            'min-xl': { min: '1279px' },
        },
        extend: {
            flexGrow: {
                2: 2,
            },
            spacing: {
                4.5: '1.125rem',
                5.5: '1.375rem',
                6.5: '1.625rem',
                7.5: '1.875rem',
                8.5: '2.125rem',
                9.5: '2.375rem',
                10.5: '2.625rem',
                11.5: '2.875rem',
                12.5: '3.125rem',
                50: '12.5rem',
            },
            // add gradient
            // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
            backgroundImage: () => ({
                'malou-color-gradient-primary': 'linear-gradient(45deg, #6A52FD 0%, #AC32B7 50%, #D71D88 100%)',
                'malou-color-gradient-success': 'linear-gradient(45deg, #25AF5C 0%, #007B31 100%)',
                'malou-color-gradient-warning': 'linear-gradient(45deg, #FFBA4C 0%, #FD8F00 100%)',
                'malou-color-gradient-error': 'linear-gradient(45deg, #EE116E 0%, #E316AA 100%)',
                'malou-color-gradient-2': 'linear-gradient(45deg, #6A52FD 0%, #8247E4 100%)',
                'malou-color-gradient-3': 'linear-gradient(45deg, #8247E4 0%, #AC32B7 100%)',
                'malou-color-gradient-4': 'linear-gradient(45deg, #8247E4 0%, #D71D88 100%)',
                'malou-color-gradient-5': 'linear-gradient(45deg, #6A52FD 0%, #AC32B7 100%)',
                'malou-color-gradient-btn': 'linear-gradient(45deg, #6A52FD 0%, #AC32B7 100%)',
                'malou-color-gradient-btn--hover': 'linear-gradient(45deg, #AC32B7 0%, #6A52FD 100%)',
                'malou-color-gradient-background--hover':
                    'linear-gradient(360deg, rgba(242, 242, 255, 0.32) 0.52%, #F2F2FF 41.67%, rgba(242, 242, 255, 0) 100%)',
                'malou-color-gradient-success': 'linear-gradient(45deg, #34b467 0%, #cfebde 100%)',
                'malou-color-gradient-instagram': 'linear-gradient(45deg, #FFBA4C 0%, #EE116E 50%, #8247E4 100%)',
            }),
            colors: {
                'malou-color-primary': '#6A52FD',
                'malou-color-primary--hover': '#604ce4',
                'malou-color-primary--disabled': '#B5A8FE',
                'malou-color-chart-purple': '#8247E4',
                'malou-color-purple--light': '#C4B4FE',
                'malou-color-chart-purple--accent': '#AC32B7',
                'malou-color-chart-pink': '#D71D88',
                'malou-color-chart-pink--accent': '#EE116E',
                'malou-color-chart-pink--light': '#FFBBC7',
                'malou-color-chart-green': '#9AEABA',

                'malou-color-text-1': '#0A2540',
                'malou-color-text-2': '#4A5E73',
                'malou-color-text-2--light': 'rgba(74, 94, 115, 0.5)',
                'malou-color-text-light': '#ADBFD3',
                'malou-color-text-white': '#FFFFFF',
                'malou-color-text-green': '#34A762',
                'malou-color-text-pink--light': '#FF8181',
                'malou-color-text-purple--light': '#987CFB',
                'malou-color-chart-purple-deep': '#5926AC',

                'malou-color-background-white': '#FFFFFF',
                'malou-color-background-light': '#F9FAFF',
                'malou-color-background-dark': '#F2F2FF',
                'malou-color-background-dark--light': '#F2F2FF1A',
                'malou-color-background-darker': '#E1DCFC',
                'malou-color-background-purple': '#F1EEFF',
                'malou-color-background-warning': '#FFF1DC',
                'malou-color-background-loader': '#E8E5EE',
                'malou-color-background-error': '#FFF1F4',
                'malou-color-background-success-dark': '#34B467',
                'malou-color-background-success': '#E0F8EA',
                'malou-color-background-pending': '#F6F4FF',
                'malou-color-background-lavender-light': '#F3E0F4',
                'malou-color-background-purple-soft': '#E6DFF3',
                'malou-color-background-pinky-light': '#E9DFF2',
                'malou-color-background-text-1': '#0A2540',

                'malou-color-state-success': '#34B467',
                'malou-color-state-success--light': '#CCEDD9',
                'malou-color-state-error': '#EE116E',
                'malou-color-state-error--light': '#F788B7',
                'malou-color-state-warn': '#FFBA4C',
                'malou-color-state-warn--light': '#fff1db',

                'malou-color-border-primary': '#F2F2FF',
                'malou-color-border-secondary': '#C4B4FE',

                'malou-color-backdrop': 'rgba(10, 37, 64, 0.2)',

                'malou-chip-background-primary': '#E9E5FF',
                'malou-chip-text-primary': '#4A5E73',
                'malou-chip-text-dark': '#0a2540',
            },
            flex: {
                2: '2 1 0%',
                3: '3 1 0%',
                4: '4 1 0%',
                5: '5 1 0%',
                6: '6 1 0%',
                7: '7 1 0%',
                8: '8 1 0%',
                9: '9 1 0%',
            },
            transitionProperty: {
                width: 'width',
                height: 'height',
            },
            maxWidth: {
                unset: 'unset',
            },
            height: {
                screen: ['100vh /* fallback for Opera, IE and etc. */', '100dvh'],
                'd-90': ['90vh /* fallback for Opera, IE and etc. */', '90dvh'],
                'd-80': ['80vh /* fallback for Opera, IE and etc. */', '80dvh'],
                'd-70': ['70vh /* fallback for Opera, IE and etc. */', '70dvh'],
                'd-60': ['60vh /* fallback for Opera, IE and etc. */', '60dvh'],
                'd-50': ['50vh /* fallback for Opera, IE and etc. */', '50dvh'],
                'd-40': ['40vh /* fallback for Opera, IE and etc. */', '40dvh'],
                'd-30': ['30vh /* fallback for Opera, IE and etc. */', '30dvh'],
                'd-20': ['20vh /* fallback for Opera, IE and etc. */', '20dvh'],
                'd-10': ['10vh /* fallback for Opera, IE and etc. */', '10dvh'],
            },
            aspectRatio: {
                '9/16': '9 / 16',
            },
            keyframes: {
                'fade-in': {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
            },
            animation: {
                'fade-in': 'fade-in 0.5s ease-in-out forwards',
            },
        },
        fontFamily: {
            'passion-one': ['Passion One', 'sans-serif'],
            'meow-script': ['Meow Script', 'cursive'],
        },
    },
    plugins: [require('@tailwindcss/container-queries')],
};
