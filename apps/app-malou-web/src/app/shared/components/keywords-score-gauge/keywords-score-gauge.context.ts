import { computed, DestroyRef, effect, inject, Injectable, Signal, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { capitalize, countBy, isNil, maxBy } from 'lodash';
import objectHash from 'object-hash';
import { BehaviorSubject, combineLatest, EMPTY, of } from 'rxjs';
import { catchError, debounceTime, filter, map, startWith, switchMap } from 'rxjs/operators';

import { KeywordsScoreDetailDto } from '@malou-io/package-dto';
import {
    ApplicationLanguage,
    BricksCategory,
    IBreakdown,
    isNotNil,
    KEYWORD_SCORE_METHOD,
    KeywordConditionCriteria,
    KeywordScoreTextType,
    LanguageCodeISO_1,
    mapLanguageStringToApplicationLanguage,
    removeDiacritics,
    toLowerCase,
    TooltipPosition,
} from '@malou-io/package-utils';

import { KeywordsService } from ':core/services/keywords.service';
import { LocalStorage } from ':core/storage/local-storage';
import {
    ConditionsInterface,
    Indication,
    KeywordsScoreGaugeType,
} from ':shared/components/keywords-score-gauge/keywords-score-gauge.interface';
import { editKeywordsScore } from ':shared/components/keywords-score-gauge/store/keywords-score.actions';
import { selectKeywordsScore } from ':shared/components/keywords-score-gauge/store/keywords-score.reducer';
import { Keyword, Restaurant, ReviewerNameValidation } from ':shared/models/';
import { RestaurantAiSettings } from ':shared/models/restaurant-ai-settings';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Injectable()
export class KeywordsScoreGaugeContext {
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _keywordsService = inject(KeywordsService);
    private readonly _store = inject(Store);

    readonly SvgIcon = SvgIcon;
    readonly TooltipPosition = TooltipPosition;
    readonly KeywordsScoreGaugeType = KeywordsScoreGaugeType;

    readonly title = signal<string>(this._translateService.instant('keywords.score'));
    readonly type = signal<KeywordsScoreGaugeType>(KeywordsScoreGaugeType.SIMPLE);
    readonly langOptions = signal<(ApplicationLanguage | string)[]>(Object.values(ApplicationLanguage));
    readonly text = signal<string | undefined>(undefined);
    readonly textType = signal<KeywordScoreTextType | undefined>(undefined);
    readonly restaurant = signal<Restaurant | null>(null);
    readonly keywords = signal<Keyword[]>([]);
    readonly lang = signal<string | null>(null);
    readonly responseTime = signal<number>(0);
    readonly reviewerName = signal<string | undefined>('');
    readonly parentElementId = signal<string | null>(null);
    readonly shouldCacheScore = signal<boolean>(true);
    readonly areDisabledBricks = signal<boolean>(false);
    readonly restaurantAiSettings = signal<RestaurantAiSettings | undefined>(undefined);
    readonly savedKeywordsScore = signal<number | null>(null);
    readonly relevantBricks = signal<IBreakdown[]>([]);
    readonly reviewerNameValidation = signal<ReviewerNameValidation | undefined>(undefined);

    private _addKeywordCallback: ((value: string) => void) | null = null;
    private _indicationListChangedCallback: ((value: Indication[]) => void) | null = null;
    private _langChangedCallback: ((value: string) => void) | null = null;

    readonly indicationsList: WritableSignal<Indication[]> = signal([]);
    readonly formattedIndicationsList: Signal<string> = computed(() => {
        const intro = this._translateService.instant('keywords_score.tips') + ' : ';
        const missingInfos = this.indicationsList()
            ?.filter((indication) => !indication.fulfilled)
            ?.map(({ shouldDisplayCurrentValue, textType, currentValue, optimalValue, minimumValue }) => {
                const text = this._translateService.instant('keywords_gauge.' + textType + '.maintext');
                const value = shouldDisplayCurrentValue ? `${currentValue}/${optimalValue ?? minimumValue}` : '';
                return '- ' + text + ' ' + value;
            });
        return missingInfos.length ? [intro, ...missingInfos].join('\n') : '';
    });

    readonly score = signal(0);
    readonly bricks$ = new BehaviorSubject<IBreakdown[]>([]);
    readonly bricksFound: WritableSignal<string[]> = signal([]);
    readonly bricksClickedOn: WritableSignal<string[]> = signal([]);
    readonly isBrickChecked = computed(() => (brick: IBreakdown): boolean => {
        const isBrickFoundInText = this.bricksFound().some((brickFound) => this._brickComparator(brick, brickFound));
        const hasBrickBeenClickedOn = this.bricksClickedOn().some((brickClickedOn) => this._brickComparator(brick, brickClickedOn));
        return isBrickFoundInText || hasBrickBeenClickedOn;
    });

    readonly brickTranslated = computed(() => (brick: IBreakdown): string => {
        const lang = this.brickLangControlValue();
        return (lang && brick.translations?.[lang]) ?? brick.text;
    });

    readonly allBricks = toSignal(this.bricks$, { initialValue: [] });

    readonly brickLangControl: FormControl<string> = new FormControl();
    readonly brickLangControlValue = toSignal(this.brickLangControl.valueChanges.pipe(startWith(null)), {
        initialValue: this.brickLangControl.value,
    });

    private readonly _previousParentElementId = signal<string | null>(null);

    private readonly _text$ = toObservable(this.text);
    private readonly _textType$ = toObservable(this.textType);
    private readonly _restaurant$ = toObservable(this.restaurant);
    private readonly _keywords$ = toObservable(this.keywords);
    private readonly _lang$ = toObservable(this.lang);
    private readonly _responseTime$ = toObservable(this.responseTime);
    private readonly _reviewerName$ = toObservable(this.reviewerName);
    private readonly _parentElementId$ = toObservable(this.parentElementId);
    private readonly _restaurantAiSettings$ = toObservable(this.restaurantAiSettings);
    private readonly _reviewerNameValidation$ = toObservable(this.reviewerNameValidation);

    constructor(private _translateService: TranslateService) {
        effect(() => {
            const savedScore = this.savedKeywordsScore();
            if (isNotNil(savedScore)) {
                this.score.set(savedScore);
            }
        });

        this._initializeDataSubscription();
    }

    setAddKeywordCallback(callback: (value: string) => void): void {
        this._addKeywordCallback = callback;
    }

    setIndicationListChangedCallback(callback: (value: Indication[]) => void): void {
        this._indicationListChangedCallback = callback;
    }

    setLangChangedCallback(callback: (value: string) => void): void {
        this._langChangedCallback = callback;
    }

    addBrick(brick: IBreakdown): void {
        if (this.areDisabledBricks()) {
            return;
        }
        this.bricksClickedOn.update((bricksClickedOn) => [...bricksClickedOn, this.brickTranslated()(brick)]);
        if (this._addKeywordCallback) {
            this._addKeywordCallback(this.brickTranslated()(brick));
        }
    }

    brickLangDisplayFn = (lang: ApplicationLanguage | string): string => {
        const isApplicationLanguage = ApplicationLanguage[lang.toUpperCase()] !== undefined;
        const isHandledCountryCode = Object.values(LanguageCodeISO_1).map(toLowerCase).includes(lang.toLowerCase());
        return isApplicationLanguage || isHandledCountryCode ? this._translateService.instant(`common.langs.${lang}`) : capitalize(lang);
    };

    getIdSuffixFn = (lang: ApplicationLanguage): string => lang;

    private _initializeDataSubscription(): void {
        const savedScore = this.savedKeywordsScore();
        if (isNotNil(savedScore)) {
            this.score.set(savedScore);
        }

        combineLatest([
            this._text$,
            this._textType$,
            this._keywords$,
            this._restaurant$,
            this._lang$,
            this.brickLangControl.valueChanges.pipe(startWith(null)),
            this._reviewerName$,
            this._responseTime$,
            this._parentElementId$,
            this._restaurantAiSettings$,
            this._reviewerNameValidation$,
        ])
            .pipe(
                debounceTime(400),
                filter(() => isNil(this.savedKeywordsScore())),
                filter(([_text, _textType, _keywords, restaurant]) => isNotNil(restaurant)),
                switchMap(
                    ([
                        text,
                        textType,
                        keywords,
                        restaurant,
                        lang,
                        selectedBricksLang,
                        reviewerName,
                        responseTime,
                        parentElementId,
                        restaurantAiSettings,
                        reviewerNameValidation,
                    ]: [
                        string | undefined,
                        KeywordScoreTextType | undefined,
                        Keyword[],
                        Restaurant,
                        string | null,
                        ApplicationLanguage,
                        string,
                        number,
                        string | null,
                        RestaurantAiSettings | undefined,
                        ReviewerNameValidation | undefined,
                    ]) => {
                        const bricksLang = this._getBricksLang(
                            keywords,
                            lang,
                            selectedBricksLang,
                            parentElementId !== this._previousParentElementId()
                        );
                        if (bricksLang !== lang && this._langChangedCallback) {
                            this._langChangedCallback(bricksLang);
                        }
                        this._previousParentElementId.set(parentElementId);

                        const restaurantName =
                            textType && this._isReviewTextType(textType) && restaurantAiSettings?.restaurantName
                                ? restaurantAiSettings.restaurantName
                                : restaurant.name;

                        const bricks = this._buildBricksFromKeywords(keywords, bricksLang, reviewerName, textType, restaurantName);
                        this.bricks$.next(bricks);
                        const bricksToProcessScoreWith = bricks
                            .filter(
                                (brick) =>
                                    brick.category !== BricksCategory.RESTAURANT_NAME && brick.category !== BricksCategory.REVIEWER_NAME
                            )
                            .map((brick) => this.brickTranslated()(brick));

                        const body = {
                            text: this._cleanText(text),
                            textType: textType ?? KeywordScoreTextType.POST,
                            bricks: bricksToProcessScoreWith,
                            venueName: this._cleanText(restaurantName, false),
                            language: bricksLang,
                            reviewerName: this._cleanText(reviewerName, false),
                            responseTime,
                            keywordScoreMethod: KEYWORD_SCORE_METHOD,
                            reviewerNameValidation,
                        };
                        const key = objectHash(body);

                        return this._store.select(selectKeywordsScore(key)).pipe(
                            switchMap((score) => {
                                if (score) {
                                    return of(score);
                                }
                                return this._keywordsService
                                    .processKeywordsScore$({
                                        ...body,
                                        shouldCacheScore: this.shouldCacheScore(),
                                    })
                                    .pipe(
                                        map((res) => {
                                            if (this.shouldCacheScore()) {
                                                this._store.dispatch(editKeywordsScore({ [key]: res }));
                                            }
                                            return res;
                                        }),
                                        catchError((err) => {
                                            console.error(err);
                                            return EMPTY;
                                        })
                                    );
                            })
                        );
                    }
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe((res) => {
                // Since we don't send restaurant name brick to score lambda,
                // we need to manually check the brick if the lambda score return that the restaurant name criteria is fulfilled
                this.bricksFound.update(() => {
                    const restaurantNameFound = !!res.restaurantNameFound;
                    const reviewerNameFound = !!res.reviewerNameFound;
                    const restaurantNameBrick = this.bricks$.value.find((brick) => brick.category === BricksCategory.RESTAURANT_NAME);
                    const reviewerNameBrick = this.bricks$.value.find((brick) => brick.category === BricksCategory.REVIEWER_NAME);
                    return [
                        ...(res.bricksFound ?? []),
                        ...(restaurantNameFound && restaurantNameBrick ? [restaurantNameBrick.text] : []),
                        ...(reviewerNameFound && reviewerNameBrick ? [reviewerNameBrick.text] : []),
                    ];
                });
                this.bricksClickedOn.set([]);
                const indications = this._computeIndications(res.details ?? []);
                this.indicationsList.set(indications);
                if (this._indicationListChangedCallback) {
                    this._indicationListChangedCallback(indications);
                }
                this.score.set(res.score);
            });
    }

    private _cleanText(text: string = '', shouldTransformToLowerCase = true): string {
        const cleanText = removeDiacritics(text).trim();
        return (shouldTransformToLowerCase ? cleanText?.toLowerCase() : cleanText) || '';
    }

    private _computeIndications(details: KeywordsScoreDetailDto[]): Indication[] {
        const detailsToShow = details.filter(
            (detail) => detail.criteria !== KeywordConditionCriteria.RESTAURANT_NAME || detail.fulfilledValue.optimalValue !== 0
        );
        const keyConditions = detailsToShow.map((detail) => detail.criteria);
        const currentIndicationsObject = this._buildCurrentIndicationsObject(detailsToShow);
        return keyConditions.map((condition) => ({
            fulfilled: currentIndicationsObject[condition].fulfilled,
            currentValue: currentIndicationsObject[condition].currentValue,
            optimalValue: currentIndicationsObject[condition].fulfilledValue?.optimalValue,
            minimumValue: currentIndicationsObject[condition].fulfilledValue?.minimumValue,
            maximumValue: currentIndicationsObject[condition].fulfilledValue?.maximumValue,
            textType: condition,
            shouldDisplayCurrentValue: this._shouldDisplayCurrentValueForCondition(condition),
            shouldDisplaySubText: this._shouldDisplaySubText(condition),
        }));
    }

    private _brickComparator(brick: IBreakdown, brickFound: string): boolean {
        return this.brickTranslated()(brick)?.toLowerCase() === brickFound.toLowerCase();
    }

    private _getBricksLang(
        keywords: Keyword[],
        lang: string | null,
        selectedBricksLang: string | null,
        parentElementHasChanged: boolean
    ): string {
        const mostOccurrencesLang = this._getMostOccurrencesKeywordsLang(keywords);
        const defaultLang = mostOccurrencesLang || LocalStorage.getLang();
        lang = lang && this.langOptions().includes(lang as ApplicationLanguage) ? lang : defaultLang;

        const bricksLang = parentElementHasChanged ? lang : (selectedBricksLang ?? lang ?? defaultLang);
        if (bricksLang !== this.brickLangControl.value) {
            this.brickLangControl.setValue(bricksLang, { emitEvent: true });
        }
        return bricksLang;
    }

    private _buildBricksFromKeywords(
        keywords: Keyword[],
        lang: string,
        reviewerName: string | undefined,
        textType: KeywordScoreTextType | undefined,
        restaurantName: string
    ): IBreakdown[] {
        if (textType === KeywordScoreTextType.LOW_RATE_REVIEW || textType === KeywordScoreTextType.LOW_RATE_TEMPLATE) {
            const defaultBricks = [{ text: restaurantName, category: BricksCategory.RESTAURANT_NAME, lang: 'fr' }];
            if (reviewerName) {
                defaultBricks.push({ text: reviewerName, category: BricksCategory.REVIEWER_NAME, lang: 'fr' });
            }
            return defaultBricks;
        }

        const selectedKeywords = (keywords || []).filter((keyword) => keyword.selected);

        const bricks: IBreakdown<string>[] = selectedKeywords
            .map((keyword): IBreakdown<string>[] => {
                const customKeyword = {
                    text: keyword.text?.toLowerCase(),
                    category: 'customerInput',
                    lang: keyword.language,
                };
                const defaultBricks = keyword.isCustomerInput ? [customKeyword] : [];

                return keyword.bricks?.length
                    ? keyword.bricks.filter((brick) => keyword.language === lang || !!brick.translations?.[lang])
                    : defaultBricks;
            })
            .flat()
            .filter((brick) => this.brickTranslated()(brick))
            .sort((a, _b) => {
                if (a.category === 'customerInput') {
                    return -1;
                }
                return 1;
            }) // used for next step, customerInput is prio if another brick has same text
            .filter((brick, index, self) => {
                const foundIndex = self.findIndex((t) => this.brickTranslated()(t) === this.brickTranslated()(brick));
                return index === foundIndex;
            }) // remove duplicate text bricks in current lang
            .sort((a, b) => {
                if (a.text.length < b.text.length) {
                    return 1;
                }
                return -1;
            });

        if (reviewerName) {
            bricks.unshift({ text: reviewerName, category: BricksCategory.REVIEWER_NAME });
        }
        bricks.unshift({ text: restaurantName, category: BricksCategory.RESTAURANT_NAME });

        return bricks;
    }

    private _getMostOccurrencesKeywordsLang(keywords: Keyword[]): ApplicationLanguage | null {
        if (keywords.length === 0) {
            return null;
        }
        const langs = keywords.filter((k) => k.selected).map((keyword) => keyword.language);
        if (langs.length === 0) {
            return null;
        }
        const langOccurrences = countBy(langs);
        const langOccurrencesKeys = Object.keys(langOccurrences);
        const getMostOccurrencesKeywordsLang = maxBy(langOccurrencesKeys, (lang) => langOccurrences[lang]) ?? null;
        return getMostOccurrencesKeywordsLang ? mapLanguageStringToApplicationLanguage(getMostOccurrencesKeywordsLang) : null;
    }

    private _buildCurrentIndicationsObject<T extends KeywordScoreTextType>(details: KeywordsScoreDetailDto[]): ConditionsInterface<T> {
        return (
            details?.reduce((acc, detail) => {
                const condition = detail.criteria;
                acc[condition] = {
                    ...acc[condition],
                    fulfilled: detail.fulfilled,
                    fulfilledValue: detail.fulfilledValue,
                    currentValue: detail.value,
                };
                return acc;
            }, {} as ConditionsInterface<T>) ?? {}
        );
    }

    private _shouldDisplayCurrentValueForCondition(condition: KeywordConditionCriteria): boolean {
        return ![
            KeywordConditionCriteria.RESTAURANT_NAME,
            KeywordConditionCriteria.RESPONSE_TIME,
            KeywordConditionCriteria.REVIEWER_NAME,
        ].includes(condition);
    }

    private _shouldDisplaySubText(condition: KeywordConditionCriteria): boolean {
        return [KeywordConditionCriteria.BRICKS_VARIETY, KeywordConditionCriteria.SORRY_WORDS].includes(condition);
    }

    private _isReviewTextType(textType: KeywordScoreTextType): boolean {
        return [KeywordScoreTextType.LOW_RATE_REVIEW, KeywordScoreTextType.HIGH_RATE_REVIEW].includes(textType);
    }
}
