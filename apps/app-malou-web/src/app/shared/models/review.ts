import { SafeHtml } from '@angular/platform-browser';
import { DateTime } from 'luxon';

import {
    LightReviewWithSegmentAnalysesDto,
    ReviewerNameValidationDto,
    ReviewWithTranslationsResponseDto,
    SemanticAnalysisInsightsReviewDto,
} from '@malou-io/package-dto';
import {
    ApplicationLanguage,
    canHaveMultipleReplies,
    canReviewBeModified,
    Civility,
    CurrencyCode,
    DELIVEROO_DAYS_UNTIL_CANT_BE_ANSWERED,
    getPlatformDefinition,
    hasDelayToReplyReview,
    isNotNil,
    ITranslations,
    PlatformKey,
    PostedStatus,
    ReviewAnalysisSentiment,
    ReviewAnalysisStatus,
    ReviewAnalysisTag,
    ReviewCannotReplyExplanation,
    SemanticAnalysisFetchStatus,
    TimeInMilliseconds,
    TranslationSource,
    UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED,
    UbereatsPromotionValue,
} from '@malou-io/package-utils';

import { SimpleUserInterface } from ':modules/user/user';
import { formatStringDate } from ':shared/helpers';
import { ReviewAnalysisHighlighter } from ':shared/helpers/analysis-highlight';
import { concatReviewTextAndRatingTags } from ':shared/helpers/review-text-and-tags';
import { IReview } from ':shared/interfaces';
import { KeywordAnalysis } from ':shared/models/keyword-analysis';
import { PrivateReview } from ':shared/models/private-review';
import { SemanticAnalysis } from ':shared/models/review-analyses';
import { CommentReviewsFilters } from ':shared/models/review-filters';
import { SegmentAnalysis } from ':shared/models/segment-analysis';
import { AppInjectorService } from ':shared/services/app-injector.service';

export interface ReviewSocialAttachment {
    urls: {
        original: string;
        small?: string;
    };
    type: string;
}

export enum ReviewType {
    RATING = 'rating',
    VISITOR_POST = 'visitor_post',
}

export class ReviewerNameValidation {
    gender: Civility;
    firstName: string;
    isFirstNameValid: boolean;
    lastName: string;
    isLastNameValid: boolean;

    public constructor(init: ReviewerNameValidation) {
        this.gender = init.gender;
        this.firstName = init.firstName;
        this.isFirstNameValid = init.isFirstNameValid;
        this.lastName = init.lastName;
        this.isLastNameValid = init.isLastNameValid;
    }

    static fromDto(dto: ReviewerNameValidationDto): ReviewerNameValidation {
        const gender: Record<string, Civility> = {
            male: Civility.MALE,
            female: Civility.FEMALE,
            other: Civility.OTHER,
        };
        return new ReviewerNameValidation({
            gender: gender[dto.gender] ?? Civility.OTHER,
            firstName: dto.firstName,
            isFirstNameValid: dto.isFirstNameValid,
            lastName: dto.lastName,
            isLastNameValid: dto.isLastNameValid,
        });
    }
}

export const DEFAULT_REVIEWER_NAME_VALIDATION: ReviewerNameValidation = new ReviewerNameValidation({
    gender: Civility.OTHER,
    firstName: '',
    isFirstNameValid: false,
    lastName: '',
    isLastNameValid: false,
});

export interface ResponseStyle {
    style: string;
    toneOfVoice: string;
    responseStructure: string;
}

export class Review implements IReview {
    _id: string;
    platformId: string;
    restaurantId: string;
    key: PlatformKey;
    socialId: string;
    socialLink: string;
    businessSocialLink: string;
    socialCreatedAt: Date;
    socialUpdatedAt: Date | null;
    rating: number;
    text?: string;
    socialTranslatedText?: string;
    lang: string;
    reviewer?: {
        profilePhotoUrl?: string;
        displayName: string;
        email?: string;
    };
    comments: ReviewReply[] = [];
    type: ReviewType;
    archived: boolean;
    updatedAt: Date;
    createdAt: Date;
    socialAttachments?: ReviewSocialAttachment[];
    wasAnsweredAutomatically?: boolean;

    ratingTags?: string[];
    menuItemReviews?: MenuItemReview[] = [];
    eaterTotalOrders?: number;
    isReplyScheduled?: boolean;
    order?: Order;

    nbDaysLeftToReply?: number | null;
    semanticAnalysis: SemanticAnalysis | null = null; // TODO: Remove everywhere when feature toggle 'release-new-semantic-analysis' is removed
    semanticAnalysisSegments?: SegmentAnalysis[] = undefined;
    semanticAnalysisFetchStatus: SemanticAnalysisFetchStatus | null = null;
    semanticAnalysisStatus?: ReviewAnalysisStatus = undefined;
    keywordsLang?: string;
    aiRelevantBricks?: {
        text: string;
        category: string;
        translationsId?: string;
        translations?: ITranslations;
    }[];
    aiRelatedBricksCount?: number;

    couldNotSendReply = false;

    translations: ITranslations | undefined = undefined;
    intelligentSubjects?: {
        subject: string;
        isDetected: boolean;
        sentiment?: ReviewAnalysisSentiment;
    }[];
    reviewerNameValidation?: ReviewerNameValidation;
    responseStyle?: ResponseStyle;

    public constructor(init?: Partial<Review>) {
        Object.assign(this, init);
        this.socialCreatedAt = new Date(this.socialCreatedAt);
        this.socialUpdatedAt = this.socialUpdatedAt ? new Date(this.socialUpdatedAt) : null;
        this.createdAt = new Date(this.createdAt);
        this.updatedAt = new Date(this.updatedAt);
        if (this.comments) {
            this.comments = this.comments.map((c) => new ReviewReply(c));
        }
        this.nbDaysLeftToReply = this._getNbDaysLeftToReply();
        this.semanticAnalysis = this.semanticAnalysis
            ? new SemanticAnalysis(this.semanticAnalysis, this.getFullReviewTextWithRatingTags() ?? '')
            : null;
        this.semanticAnalysisSegments = this.semanticAnalysisSegments
            ? SegmentAnalysis.sortSegmentsByPositionInText(this.semanticAnalysisSegments, this.getFullReviewTextWithRatingTags() ?? null)
            : undefined;
        this.semanticAnalysisSegments = this.semanticAnalysisSegments
            ? SegmentAnalysis.filterSegmentAnalysesByTag(this.semanticAnalysisSegments, ReviewAnalysisTag.OVERALL_EXPERIENCE)
            : undefined;
        this.semanticAnalysisSegments = this.semanticAnalysisSegments
            ? SegmentAnalysis.filterSegmentAnalysesBySentiment(this.semanticAnalysisSegments, ReviewAnalysisSentiment.NEUTRAL)
            : undefined;

        this.semanticAnalysisStatus = Review.mapToReviewAnalysisStatus(this);
    }

    static fromReviewWithTranslationsResponseDto(reviewDto: ReviewWithTranslationsResponseDto): Review {
        return new Review({
            ...reviewDto,
            socialCreatedAt: reviewDto.socialCreatedAt ? new Date(reviewDto.socialCreatedAt) : undefined,
            socialUpdatedAt: reviewDto.socialUpdatedAt ? new Date(reviewDto.socialUpdatedAt) : undefined,
            comments: reviewDto.comments.map(
                (comment) =>
                    new ReviewReply({
                        ...comment,
                        socialUpdatedAt: comment.socialUpdatedAt ? new Date(comment.socialUpdatedAt) : undefined,
                        author: comment.author
                            ? {
                                  _id: comment.author._id ?? '',
                                  name: comment.author.name,
                                  picture: comment.author.picture,
                              }
                            : undefined,
                    })
            ),
            translations: reviewDto.translations,
            semanticAnalysis: SemanticAnalysis.fromReviewAnalysisDto(reviewDto.semanticAnalysis, reviewDto.text ?? ''),
            semanticAnalysisFetchStatus: reviewDto.semanticAnalysisFetchStatus,
            semanticAnalysisSegments: reviewDto.semanticAnalysisSegments?.map((segment) => new SegmentAnalysis(segment)),
            reviewerNameValidation: reviewDto.reviewerNameValidation
                ? ReviewerNameValidation.fromDto(reviewDto.reviewerNameValidation)
                : undefined,
            responseStyle: reviewDto.responseStyle,
        });
    }

    static mapToReviewAnalysisStatus(review: Review | PrivateReview): ReviewAnalysisStatus | undefined {
        if (!review) {
            return ReviewAnalysisStatus.PENDING;
        }
        if (!review.doesPlatformHaveAnalysis()) {
            return ReviewAnalysisStatus.UNSUPPORTED_PLATFORM;
        }
        if (review.semanticAnalysisSegments?.length) {
            return ReviewAnalysisStatus.DONE;
        }
        if (review.semanticAnalysisFetchStatus === SemanticAnalysisFetchStatus.DONE && !review.semanticAnalysisSegments?.length) {
            return ReviewAnalysisStatus.NO_RESULTS;
        }
        const oneMonthAgo = DateTime.now().minus({ month: 1 });
        if (DateTime.fromJSDate(review.socialCreatedAt) < oneMonthAgo) {
            return ReviewAnalysisStatus.REVIEW_TOO_OLD;
        }
        switch (review.semanticAnalysisFetchStatus) {
            case SemanticAnalysisFetchStatus.NO_RESULTS:
                return ReviewAnalysisStatus.NO_RESULTS;
            case SemanticAnalysisFetchStatus.FAILED:
                return ReviewAnalysisStatus.FAILED;
            case SemanticAnalysisFetchStatus.DONE:
                return ReviewAnalysisStatus.DONE;
            default:
                return ReviewAnalysisStatus.PENDING;
        }
    }

    static getRatingRange(rating: number | undefined): number[] {
        if (!rating) {
            return [0];
        }
        return Number.isInteger(rating) ? [rating] : [Math.floor(rating), Math.ceil(rating)];
    }

    public getFullReviewTextWithRatingTags(options?: { showTranslation: boolean; language: string }): string {
        const reviewText = options?.showTranslation ? this.getTranslation(options?.language) : (this.text ?? '');
        return concatReviewTextAndRatingTags(reviewText, this.ratingTags);
    }

    public getCommentOption(): CommentReviewsFilters | null {
        return isNotNil(this.text) || this.ratingTags?.length || this.menuItemReviews?.length
            ? CommentReviewsFilters.WITH_TEXT
            : CommentReviewsFilters.WITHOUT_TEXT;
    }

    public canBeLabelized(): boolean {
        return this.text !== undefined || this.text !== null || this.text !== '';
    }

    public canBeReplied(): boolean {
        return this.getCannotReplyExplanation() === null;
    }

    /**
     * Returns the reason why the review cannot be replied to.
     * If the review can be replied to, it returns null.
     */
    public getCannotReplyExplanation(): ReviewCannotReplyExplanation | null {
        switch (this.key) {
            case PlatformKey.UBEREATS: {
                const hasOnlyRejectedComment =
                    this.comments.length === this.comments.filter((comment) => comment.posted === PostedStatus.REJECTED).length;
                const hasComments = this.comments?.length > 0;
                return (hasOnlyRejectedComment || !hasComments) && this.canBeRepliedBeforeTimeLimit()
                    ? null
                    : ReviewCannotReplyExplanation.IS_TOO_OLD;
            }
            case PlatformKey.DELIVEROO: {
                if (!this.text?.length) {
                    return ReviewCannotReplyExplanation.HAS_NO_TEXT;
                }
                const hasComments = this.comments?.length > 0;
                return !hasComments && this.canBeRepliedBeforeTimeLimit() ? null : ReviewCannotReplyExplanation.IS_TOO_OLD;
            }
            // TODO: handle DoorDash answer reply if we manage to get something else than a 403
            // case PlatformKey.DOORDASH: {
            //     return this.canBeRepliedBeforeTimeLimit() ? null : ReviewCannotReplyExplanation.IS_TOO_OLD;
            // }
            case PlatformKey.DOORDASH:
            case PlatformKey.FOURSQUARE:
            case PlatformKey.RESY:
            case PlatformKey.SEVENROOMS:
                return ReviewCannotReplyExplanation.PLATFORM_DOESNT_SUPPORT_REPLY;
            case PlatformKey.LAFOURCHETTE:
                return !this.text?.length ? ReviewCannotReplyExplanation.HAS_NO_TEXT : null;
            default:
                return null;
        }
    }

    public canHaveMultipleReplies(): boolean {
        return canHaveMultipleReplies(this.key);
    }

    public hasReply(): boolean {
        return this.comments?.length > 0;
    }

    public hasOnlyPendingComment(): boolean {
        return (this.comments ?? []).every(
            (comment) => comment.posted && [PostedStatus.PENDING, PostedStatus.RETRY].includes(comment.posted)
        );
    }

    public hasRejectedComment(): boolean {
        return (this.comments ?? []).some((comment) => comment.posted && [PostedStatus.REJECTED].includes(comment.posted));
    }

    public canBeEdited(): boolean {
        const isUbereats = this.key === PlatformKey.UBEREATS;
        const hasOnlyRejectedComment =
            this.comments.length === this.comments.filter((comment) => comment.posted === PostedStatus.REJECTED).length;
        if (isUbereats && hasOnlyRejectedComment) {
            return true;
        }
        return canReviewBeModified(this.key);
    }

    public isPrivate(): this is PrivateReview {
        return false;
    }

    public getMenuItemReviews(): MenuItemReview[] {
        return this.menuItemReviews ?? [];
    }

    public getUbereatsPromotionAmountInHundredths(): number | undefined {
        return this.comments[0]?.ubereatsPromotionAmountInHundredths;
    }

    public getEaterTotalOrders(): number | undefined {
        return this.eaterTotalOrders;
    }

    public getOrderTotal(): number | undefined {
        return this.order?.orderTotal;
    }

    public getOrderCurrencyCode(): CurrencyCode | undefined {
        return this.order?.currencyCode;
    }

    public getNbDaysUntilCantBeAnswered(): number | null {
        switch (this.key) {
            case PlatformKey.DELIVEROO:
                return DELIVEROO_DAYS_UNTIL_CANT_BE_ANSWERED;
            case PlatformKey.UBEREATS:
                return UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED;
            // TODO: handle DoorDash answer reply if we manage to get something else than a 403
            // case PlatformKey.DOORDASH:
            //     return DOORDASH_DAYS_UNTIL_CANT_BE_ANSWERED;
            default:
                return null;
        }
    }

    public canBeRepliedBeforeTimeLimit(): boolean {
        const diffDays = DateTime.now().diff(DateTime.fromJSDate(this.socialCreatedAt), 'day').toObject().days;
        return (diffDays ?? 0) < (this.getNbDaysUntilCantBeAnswered() ?? 0);
    }

    public getReviewDate(): string {
        if (this.socialUpdatedAt?.getTime()) {
            return formatStringDate(this.socialUpdatedAt);
        }
        return formatStringDate(this.socialCreatedAt);
    }

    public getReviewOrderDate(): string | null {
        return this.order?.deliveredAt ? formatStringDate(this.order.deliveredAt) || null : null;
    }

    public hasAttachments(): boolean {
        return (this.socialAttachments?.length ?? 0) > 0;
    }

    public hasText(): boolean {
        return !!this.text?.length;
    }

    public getDisplayName(): string {
        return this.reviewer?.displayName ?? '';
    }

    public getFirstCommentDate(): Date | undefined {
        const date = this.comments?.find((r) => r.posted === PostedStatus.POSTED)?.socialUpdatedAt;
        return date ? new Date(date) : undefined;
    }

    public hasScanId(): boolean {
        return false;
    }

    public getSocialAttachments(): ReviewSocialAttachment[] {
        return this.socialAttachments ?? [];
    }

    public getWasAnsweredAutomatically(): boolean {
        return this.wasAnsweredAutomatically ?? false;
    }

    public getNbDaysLeftToReply(): number | null {
        return this.nbDaysLeftToReply ?? null;
    }

    public getComments(): ReviewReply[] {
        switch (this.key) {
            case PlatformKey.FACEBOOK:
            case PlatformKey.ZENCHEF:
            case PlatformKey.OPENTABLE:
                return this.comments;
            default:
                return this.comments[this.comments.length - 1] ? [this.comments[this.comments.length - 1]] : [];
        }
    }

    public hasToHideReplySection(): boolean {
        switch (this.key) {
            case PlatformKey.FACEBOOK:
            case PlatformKey.ZENCHEF:
            case PlatformKey.OPENTABLE:
                return false;
            default:
                return this.comments.length > 0;
        }
    }

    public copyWith(review: Partial<Review>): Review {
        return new Review({
            ...this,
            ...review,
        });
    }

    public hasTranslations(lang: string): boolean {
        return !!this.translations?.[lang];
    }

    public addTranslation(text: string, language: ApplicationLanguage, source: TranslationSource): ITranslations {
        return this.translations
            ? { ...this.translations, [language]: text }
            : {
                  id: 'fakeId',
                  [language]: text,
                  language,
                  source,
              };
    }

    public getTranslation(lang: string): string {
        return this.translations?.[lang] ?? this.text;
    }

    private _getNbDaysLeftToReply(): number | null {
        if (!hasDelayToReplyReview(this.key)) {
            return null;
        }
        const createdAt = this.socialCreatedAt;
        const createdAtStartDate = createdAt.setHours(0, 0, 0, 0);
        const fourteenDaysAfterReception =
            new Date(createdAtStartDate).getTime() + TimeInMilliseconds.DAY * (this.getNbDaysUntilCantBeAnswered() ?? 1);
        const today = new Date().setHours(0, 0, 0, 0);
        const todayDateTime = new Date(today).getTime();
        const diffTime = fourteenDaysAfterReception - todayDateTime;
        if (diffTime < 0) {
            return 0;
        }
        const diffDays = Math.ceil(diffTime / TimeInMilliseconds.DAY);
        return diffDays;
    }

    doesPlatformHaveAnalysis(): boolean {
        return getPlatformDefinition(this.key)?.hasReviewSemanticAnalysis ?? false;
    }
}

export class ReviewReply {
    _id: string;
    socialId: string;
    text: string;
    socialTranslatedText?: string;
    keywordAnalysis?: KeywordAnalysis;
    socialUpdatedAt?: Date;
    posted?: PostedStatus;
    user?: { socialId: string };
    isMalou: boolean;
    author: SimpleUserInterface;
    templateIdUsed?: string;
    isRepliedFromAggregatedView?: boolean;
    ubereatsPromotionValue?: UbereatsPromotionValue;
    ubereatsPromotionAmountInHundredths?: number;

    public constructor(init?: Partial<ReviewReply>) {
        Object.assign(this, init);
    }
}

export class ReviewFactory {
    static createTestReview(): Review {
        return new Review({
            _id: 'some review id',
            platformId: 'some platform id',
            restaurantId: 'some restaurant id',
            key: PlatformKey.GMB,
            socialId: 'some social id',
            socialLink: 'some social link',
            socialCreatedAt: new Date(),
            socialUpdatedAt: new Date(),
            rating: 4,
            text: 'some text',
            lang: 'fr',
            reviewer: {
                profilePhotoUrl: 'some url',
                displayName: 'Hugo Jallet',
                email: '<EMAIL>',
            },
            comments: [],
            type: ReviewType.RATING,
            updatedAt: new Date(),
            createdAt: new Date(),
        });
    }
}

export class MenuItemReview {
    socialId: string;
    rating: boolean;
    name: string;
    comment: string;
    tags: string[];
}

class Order {
    workflowId?: string;
    deliveredAt?: Date;
    orderTotal?: number;
    currencyCode?: CurrencyCode;
    appVariant?: string;
}

export type ReviewWithAnalysis = Pick<
    Review,
    | '_id'
    | 'socialId'
    | 'text'
    | 'key'
    | 'semanticAnalysis'
    | 'semanticAnalysisSegments'
    | 'rating'
    | 'socialCreatedAt'
    | 'reviewer'
    | 'restaurantId'
>;

export class LightReviewWithSegmentAnalyses {
    id: string;
    text: string;
    restaurantId: string;
    rating: number;
    key: PlatformKey;
    socialId: string;
    semanticAnalysisSegments: SegmentAnalysis[];
    translations?: ITranslations = undefined;

    constructor(init: LightReviewWithSegmentAnalyses) {
        this.id = init.id;
        this.text = init.text;
        this.restaurantId = init.restaurantId;
        this.rating = init.rating;
        this.key = init.key;
        this.socialId = init.socialId;
        this.semanticAnalysisSegments = init.semanticAnalysisSegments;
        this.translations = init.translations;
    }

    static fromDto(dto: LightReviewWithSegmentAnalysesDto): LightReviewWithSegmentAnalyses {
        return new LightReviewWithSegmentAnalyses({
            ...dto,
            semanticAnalysisSegments: dto.semanticAnalysisSegments.map((segment) => new SegmentAnalysis(segment)),
        });
    }
}

export class SemanticAnalysisInsightsReview {
    id: string;
    restaurantId: string;
    key: PlatformKey;
    socialId: string;
    text: string;
    rating: number;
    lang: string;
    reviewer: {
        displayName: string;
    };
    socialCreatedAt: Date;
    sentiment: ReviewAnalysisSentiment;
    semanticAnalysisSegments: SegmentAnalysis[];
    translations: ITranslations | undefined = undefined;
    ratingTags?: string[];
    menuItemReviews?: MenuItemReview[] = [];

    constructor(init?: Partial<SemanticAnalysisInsightsReview>) {
        Object.assign(this, init);
    }

    static fromDto(dto: SemanticAnalysisInsightsReviewDto): SemanticAnalysisInsightsReview {
        return new SemanticAnalysisInsightsReview({
            ...dto,
            socialCreatedAt: new Date(dto.socialCreatedAt),
            semanticAnalysisSegments: dto.semanticAnalysisSegments?.map((segment) => new SegmentAnalysis(segment)) || [],
            reviewer: {
                displayName: dto.reviewer.displayName || '',
            },
        });
    }

    getFullReviewTextWithRatingTags(options?: { showTranslation: boolean; language: string }): string {
        const reviewText = options?.showTranslation ? this.getTranslation(options?.language) : this.text;
        return concatReviewTextAndRatingTags(reviewText, this.ratingTags);
    }

    getHighlightedText(): SafeHtml {
        const textHighlighter = AppInjectorService.getInjector().get(ReviewAnalysisHighlighter);
        return textHighlighter.getHighlightedReviewTextHtml({ review: this, isNewSemanticAnalysisFeatureEnabled: true });
    }

    getMenuItemReviews(): MenuItemReview[] {
        return this.menuItemReviews ?? [];
    }

    public isPrivate(): this is PrivateReview {
        return false;
    }

    public hasTranslations(lang: string): boolean {
        return !!this.translations?.[lang];
    }

    public getTranslation(lang: string): string {
        return this.translations?.[lang] ?? this.text;
    }

    private _getHighlightedSegmentText(segmentText: string): string {
        const color = this._getHighlightedColor();
        return `<span style="color: ${color}; font-weight: 500;">${segmentText}</span>`;
    }

    private _getHighlightedColor(): string {
        return this.sentiment === ReviewAnalysisSentiment.POSITIVE ? '#34B467' : '#EE116E';
    }
}
