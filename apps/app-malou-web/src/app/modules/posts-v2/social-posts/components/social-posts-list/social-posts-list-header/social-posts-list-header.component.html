@let userCanManagePost = CaslAction.MANAGE | caslAble: CaslSubject.SOCIAL_POST;

<div class="flex items-center justify-between border-b border-malou-color-border-primary bg-malou-color-background-white px-6 py-2">
    @if (isSelecting()) {
        <div>
            <mat-checkbox
                color="primary"
                [checked]="allPostsSelected()"
                (click)="$event.stopPropagation()"
                (change)="selectAllSocialPosts($event)">
                @if (allPostsSelected()) {
                    <span class="malou-text-12--semibold ml-2.5 text-malou-color-primary">
                        {{ 'common.unselect_all_with_count' | translate: { count: totalPostsCount() } }}
                    </span>
                } @else {
                    <span class="malou-text-12--semibold ml-2.5 text-malou-color-primary">
                        {{ 'common.select_all_with_count' | translate: { count: totalPostsCount() } }}
                    </span>
                }
            </mat-checkbox>
        </div>
        <div class="flex h-[50px] items-center gap-x-2">
            @if (atLeastOnePostInList()) {
                <button class="malou-btn-flat" mat-button (click)="setIsSelecting(false)">
                    {{ 'common.cancel' | translate }}
                </button>

                <div
                    [matTooltip]="
                        userCanManagePost
                            ? selectedPosts().length
                                ? selectedPosts().length > 10
                                    ? ('social_posts.header.max_10_posts_to_duplicate' | translate)
                                    : ''
                                : ('social_posts.header.select_post_to_duplicate_bulk' | translate)
                            : ('casl.wrong_role' | translate)
                    ">
                    <button
                        class="malou-btn-icon--secondary"
                        mat-icon-button
                        [matMenuTriggerFor]="duplicateActions"
                        [disabled]="!selectedPosts().length || !userCanManagePost || selectedPosts().length > 10">
                        <mat-icon color="primary" [svgIcon]="SvgIcon.DUPLICATE"></mat-icon>
                    </button>
                </div>

                <div [matTooltip]="userCanManagePost ? deletePostsTooltip() : ('casl.wrong_role' | translate)">
                    <button
                        class="malou-btn-icon--secondary"
                        id="tracking_social_posts_init_delete_bulk_v2"
                        mat-icon-button
                        [disabled]="!canDeletePosts() || !userCanManagePost"
                        (click)="onDeleteSelection()">
                        <mat-icon color="warn" [svgIcon]="SvgIcon.TRASH"></mat-icon>
                    </button>
                </div>
            }
        </div>
    } @else {
        <div class="flex items-center gap-x-1">
            @for (filterOptionAndCount of filterOptionsAndCount(); track filterOptionAndCount.filterOption) {
                @let filterOption = filterOptionAndCount.filterOption;
                @let count = filterOptionAndCount.count;
                <div
                    class="flex cursor-pointer gap-x-[2px] rounded p-2 text-malou-color-text-1"
                    id="tracking_social_posts_list_filter_{{ filterOption }}_v2"
                    [ngClass]="{
                        'malou-text-12--regular': filterOption !== selectedFilter(),
                        'malou-text-12--semibold bg-malou-color-background-dark': filterOption === selectedFilter(),
                        '!text-malou-color-chart-pink--accent': filterOption === SocialPostsListFilter.ERROR,
                    }"
                    (click)="selectFilter(filterOption)">
                    <div>{{ filterOption | enumTranslate: 'social_posts_list_filter' }}</div>
                    @if (count && filterOption !== SocialPostsListFilter.ALL) {
                        <div class="italic">({{ count }})</div>
                    }
                </div>
            }
        </div>

        <div class="flex items-center gap-x-1">
            <button class="malou-btn-flat" mat-button (click)="setIsSelecting(true)">
                {{ 'common.select' | translate }}
            </button>

            <app-create-social-post-menu-button
                (createPost)="onCreatePost()"
                (createReelOrTikTok)="onCreateReelOrTikTok()"></app-create-social-post-menu-button>
        </div>
    }
</div>

<mat-menu class="malou-mat-menu malou-box-shadow !rounded-[10px]" #duplicateActions="matMenu">
    <button
        id="tracking_social_post_duplicate_here_bulk_v2"
        mat-menu-item
        (click)="onDuplicateSelection(DuplicationDestination.HERE, PostSource.SOCIAL)">
        <span class="malou-text-14--regular">{{ 'common.here' | translate }}</span>
    </button>
    @if (isRestaurantLocalBusiness()) {
        <div [matTooltip]="(isGoogleConnected$ | async) ? '' : ('social_post.connect_google' | translate)">
            <button
                id="tracking_social_post_init_duplicate_ref_bulk_v2"
                mat-menu-item
                [disabled]="!(isGoogleConnected$ | async)"
                (click)="onDuplicateSelection(DuplicationDestination.HERE, PostSource.SEO)">
                <span class="malou-text-14--regular">{{ 'posts.duplicate_in_seo' | translate }}</span>
            </button>
        </div>
    }
    <button mat-menu-item [matMenuTriggerFor]="duplicateOutsideActions">
        <div class="flex w-full items-center justify-between">
            <span class="malou-text-14--regular">{{ 'common.to_other_venues' | translate }}</span>
            <mat-icon class="!mr-0 ml-3 !h-4 !w-4" color="primary" [svgIcon]="SvgIcon.CHEVRON_RIGHT"></mat-icon>
        </div>
    </button>
</mat-menu>

<mat-menu class="malou-mat-menu malou-box-shadow !rounded-[10px]" #duplicateOutsideActions="matMenu">
    <button
        id="tracking_social_posts_init_duplicate_to_other_restaurants_bulk_v2"
        mat-menu-item
        (click)="onDuplicateSelection(DuplicationDestination.OUT, PostSource.SOCIAL)">
        <span class="malou-text-14--regular">{{ 'social_posts.duplicate_in_social' | translate }}</span>
    </button>
    <button
        id="tracking_social_posts_init_duplicate_ref_to_other_restaurants_bulk_v2"
        mat-menu-item
        (click)="onDuplicateSelection(DuplicationDestination.OUT, PostSource.SEO)">
        <span class="malou-text-14--regular">{{ 'social_posts.duplicate_in_seo' | translate }}</span>
    </button>
</mat-menu>
