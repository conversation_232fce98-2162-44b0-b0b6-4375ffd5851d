<div class="hide-scrollbar h-full overflow-y-auto">
    <ng-container *ngTemplateOutlet="shouldShowLoading() ? loadingShimmer : posts"></ng-container>
</div>

<ng-template #loadingShimmer>
    <div class="h-full w-full overflow-hidden">
        <app-skeleton skeletonClass="h-full w-full secondary-bg p-6.5 !rounded-[0]" [useContainer]="false"></app-skeleton>
    </div>
</ng-template>

<ng-template #posts>
    <div class="h-full w-full overflow-hidden bg-white">
        @if (isIgConnected()) {
            @if (feed().length; as feedLength) {
                <div
                    class="hide-scrollbar h-full w-full overflow-y-auto"
                    infinite-scroll
                    [infiniteScrollDistance]="2"
                    [scrollWindow]="false"
                    [infiniteScrollContainer]="scrollContainerSelector"
                    (scrolled)="onScroll()"
                    #scrollContainerSelector>
                    <div
                        class="relative flex w-full flex-wrap gap-[2px] transition-all"
                        cdkDropList
                        cdkDropListOrientation="mixed"
                        [cdkDropListData]="feed()"
                        [cdkDropListSortPredicate]="sortPredicate"
                        (cdkDropListDropped)="onDropListDropped($event)">
                        @for (feedItem of feed(); track feedItem.postId; let index = $index) {
                            @let canBeDragged = canFeedItemBeDragged | applyPure: feedItem;
                            <div
                                class="aspect-[4/5] w-[26%] flex-grow"
                                cdkDrag
                                [cdkDragStartDelay]="{ touch: 200, mouse: 0 }"
                                [cdkDragData]="feedItem"
                                [cdkDragDisabled]="!canBeDragged"
                                [ngClass]="{
                                    'border border-malou-color-state-error': feedItem.published === PostPublicationStatus.ERROR,
                                }"
                                (click)="onFeedItemClicked(feedItem)"
                                #postRef>
                                <!-- The attribute [shouldLazyLoadMedia]="index > MEDIA_WITHOUT_LAZY_LOAD_COUNT" serves two purposes:
                                    1. It ensures that the first few images are loaded eagerly, as they will be displayed to the user immediately.
                                    2. It prevents image flickering when dragging a media item (we need to consider to find a better way to fix this flickering).
                                        - Only the first few images are loaded eagerly, which is acceptable because the number of draggable media items
                                        (such as in a draft post) should not exceed MEDIA_WITHOUT_LAZY_LOAD_COUNT. -->
                                <app-social-post-media-item
                                    [ngClass]="{
                                        'cursor-pointer': !canBeDragged,
                                        'cursor-grab': canBeDragged,
                                    }"
                                    [media]="feedItem.media"
                                    [icon]="feedItem | applySelfPure: 'getPostTypeIcon'"
                                    [status]="getFeedItemStatus | applyPure: feedItem"
                                    [customStatusContainerClass]="{
                                        'bg-malou-color-state-error': feedItem.published === PostPublicationStatus.ERROR,
                                        'bg-malou-color-state-warn': feedItem.published === PostPublicationStatus.PENDING,
                                        'bg-malou-color-primary': feedItem.published === PostPublicationStatus.DRAFT,
                                    }"
                                    [shouldLazyLoadMedia]="index > MEDIA_WITHOUT_LAZY_LOAD_COUNT"
                                    (refreshMedia)="onRefreshPost(feedItem)"></app-social-post-media-item>
                            </div>
                        }
                        @if (feedLength % 3 === 1) {
                            <div class="aspect-square w-[26%] flex-grow"></div>
                            <div class="aspect-square w-[26%] flex-grow"></div>
                        } @else if (feedLength % 3 === 2) {
                            <div class="aspect-square w-[26%] flex-grow"></div>
                        }
                    </div>
                </div>
            } @else {
                <div class="grid h-full w-full place-items-center px-7.5">
                    <div class="flex flex-col items-center gap-y-6">
                        <img height="100" width="100" [src]="'Goggles' | illustrationPathResolver" />
                        <div class="malou-text-14--bold text-center">
                            {{ 'social_posts.feed_empty' | translate }}
                            <div class="malou-text-10--regular mt-3 text-center">
                                {{ 'social_posts.not_connected_subtext' | translate }}
                            </div>
                        </div>
                    </div>
                </div>
            }
        } @else {
            <div class="grid h-full w-full place-items-center px-7.5">
                <div class="flex flex-col items-center gap-y-5">
                    <img height="100" width="100" [src]="'Pizza' | illustrationPathResolver" />
                    <div class="malou-text-14--bold text-center leading-7 text-malou-color-text-1">
                        {{ 'social_posts.feed.instagram_not_connected' | translate }}
                    </div>
                    <button class="malou-btn-raised--primary" mat-raised-button (click)="navigateToPlatforms()">
                        {{ 'social_posts.feed.connect_instagram' | translate }}
                    </button>
                </div>
            </div>
        }
    </div>
</ng-template>
