import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { EMPTY } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';

import { RestaurantsService } from ':core/services/restaurants.service';

import * as AdminActions from './admin.actions';

@Injectable()
export class AdminEffect {
    loadUserRestaurants$ = createEffect(() =>
        this._actions$.pipe(
            ofType(AdminActions.loadUserRestaurants),
            mergeMap(() =>
                this._restaurantsService.index('name access logo address').pipe(
                    map((restaurants) => AdminActions.editUserRestaurants({ userRestaurants: restaurants })),
                    catchError((err) => {
                        console.warn('err :', err);
                        return EMPTY;
                    })
                )
            )
        )
    );

    constructor(
        private readonly _actions$: Actions,
        private readonly _restaurantsService: RestaurantsService
    ) {}
}
