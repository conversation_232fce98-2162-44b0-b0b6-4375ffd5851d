import { Component, Input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { PostType } from '@malou-io/package-utils';

import { Post } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';

const DEFAULT_MEDIA_DURATION = 5;

@Component({
    selector: 'app-media-duration',
    imports: [MatIconModule, PluralTranslatePipe, EnumTranslatePipe, MatButtonModule],
    templateUrl: './media-duration.component.html',
    styleUrls: ['./media-duration.component.scss'],
})
export class MediaDurationComponent {
    @Input() post: Post;
    @Input() duration = DEFAULT_MEDIA_DURATION;
    mediaTypeToTranslateKey = {
        [PostType.IMAGE]: 'single_image',
        [PostType.VIDEO]: 'single_video',
    };
}
