import { Component, inject, OnInit, signal } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { LocalStorageService } from ':core/storage/local-storage.service';
import { ReviewsService } from ':modules/reviews/reviews.service';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { PrivateReview } from ':shared/models/private-review';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';

export interface ScanPrivateReviewParams {
    restaurantId: string;
    rating: number;
    scanId: string;
}

interface WheelOfFortuneParams {
    wheelOfFortuneId: string;
    redirectionPlatform: string;
}

@Component({
    selector: 'app-scan-private-review',
    templateUrl: './scan-private-review.component.html',
    styleUrls: ['./scan-private-review.component.scss'],
    imports: [TranslateModule, MatIconModule, IllustrationPathResolverPipe, MatButtonModule, TextAreaComponent, MalouSpinnerComponent],
})
export class ScanPrivateReviewComponent implements OnInit {
    private readonly _route = inject(ActivatedRoute);
    private readonly _router = inject(Router);
    private readonly _reviewsService = inject(ReviewsService);
    private readonly _translate = inject(TranslateService);
    private readonly _localStorageService = inject(LocalStorageService);

    readonly Illustration = Illustration;

    readonly initializing = signal(true);
    readonly isError = signal(false);
    readonly isReviewSubmitted = signal(false);

    readonly textAreaControl = new FormControl<string>('', Validators.required);

    private _params: ScanPrivateReviewParams;
    private _wheelOfFortuneParams: WheelOfFortuneParams;

    ngOnInit(): void {
        const { restaurantId, rating, scanId, wofId, redirectionPlatform } = this._route.snapshot.queryParams;
        if (!restaurantId || !rating || !scanId) {
            this.isError.set(true);
            this.initializing.set(false);
            return;
        }
        this._params = { restaurantId, rating: parseInt(rating, 10), scanId };
        this._wheelOfFortuneParams = { wheelOfFortuneId: wofId, redirectionPlatform };
        this.initializing.set(false);
    }

    submitReview(): void {
        const privateReview: Partial<PrivateReview> = {
            restaurantId: this._params.restaurantId,
            text: this.textAreaControl.value || '',
            lang: this._translate.currentLang,
            scanId: this._params.scanId,
            socialCreatedAt: new Date(),
            rating: this._params.rating,
            archived: false,
        };
        this._reviewsService.createPrivateReview(privateReview).subscribe({
            next: () => {
                this.isReviewSubmitted.set(true);
                if (this._wheelOfFortuneParams.wheelOfFortuneId && this._wheelOfFortuneParams.redirectionPlatform) {
                    this._localStorageService.pushLeavedReviewWheelOfFortuneInLocalStorage({
                        wheelOfFortuneId: this._wheelOfFortuneParams.wheelOfFortuneId,
                        restaurantId: this._params.restaurantId,
                        redirectionPlatform: this._wheelOfFortuneParams.redirectionPlatform,
                    });
                    this._router.navigate(['wheel-of-fortune'], {
                        queryParams: {
                            wofId: this._wheelOfFortuneParams.wheelOfFortuneId,
                            restaurantId: this._params.restaurantId,
                            isFromTotem: false,
                        },
                    });
                }
            },
            error: (err) => {
                console.error('Error when submitting private review', err);
                this.isError.set(true);
            },
        });
    }
}
