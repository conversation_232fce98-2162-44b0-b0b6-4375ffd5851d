{"dependencies": {"@astrojs/sitemap": "^3.4.0", "@malou-io/package-dto": "workspace:*", "@tailwindcss/vite": "^4.1.6", "astro": "^5.7.13", "i18next": "^24.2.3", "ico-endec": "^0.1.6", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "potrace": "^2.1.8", "sharp": "^0.33.5", "tailwindcss": "^4.0.11"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/gtag.js": "^0.0.20", "@types/leaflet": "^1.9.19", "@types/leaflet.markercluster": "^1.5.5", "axios": "^1.9.0", "dotenv": "^16.5.0", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.2", "tsx": "^4.20.3", "vite": "^6.3.5"}, "name": "@malou-io/store-locator", "scripts": {"astro": "astro", "deploy": "pnpm run type-check && astro build", "dev": "astro dev", "format": "prettier  . --write", "format:check": "prettier . --check", "predeploy": "tsx ./src/utils/generate-organization-assets.ts", "predev": "tsx ./src/utils/generate-organization-assets.ts", "preview": "pnpm run type-check && astro preview", "type-check": "astro check"}, "type": "module", "version": "0.0.1"}