import sitemap from '@astrojs/sitemap';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig, envField } from 'astro/config';

import { config } from './config';

// https://astro.build/config
export default defineConfig({
    // https://docs.astro.build/en/guides/environment-variables/#basic-usage
    env: {
        schema: {
            ORGANIZATION_ID: envField.string({
                context: 'server',
                access: 'secret',
            }),
            API_KEY: envField.string({
                context: 'server',
                access: 'secret',
            }),
            API_BASE_URL: envField.string({
                context: 'server',
                access: 'secret',
            }),
            ENVIRONMENT: envField.string({
                context: 'server',
                access: 'secret',
            }),
        },
    },
    image: {
        // Images from these domains will be optimized
        // https://docs.astro.build/fr/guides/images/#autoriser-les-images-distantes
        remotePatterns: [
            {
                protocol: 'https',
                hostname: '**.amazonaws.com',
            },
        ],
    },
    // https://tailwindcss.com/docs/installation/framework-guides/astro
    vite: {
        plugins: [tailwindcss()],
    },
    // https://docs.astro.build/en/guides/integrations-guide/sitemap/
    site: config.organizationBaseUrl,
    integrations: [sitemap()],
});
