import { Component, computed, inject, input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChartDataset, ChartOptions, ChartType } from 'chart.js';
import groupBy from 'lodash.groupby';
import { NgChartsModule } from 'ng2-charts';

import { PartialRecord, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { ChartDataArray, malouChartColorGreen, malouChartColorRed } from ':shared/helpers';
import { DiagnosticReviewSemanticAnalysisSegment } from ':shared/models';

const DEFAULT_DISPLAYED_TAGS: ReviewAnalysisTag[] = [
    ReviewAnalysisTag.FOOD,
    ReviewAnalysisTag.SERVICE,
    ReviewAnalysisTag.ATMOSPHERE,
    ReviewAnalysisTag.PRICE,
    ReviewAnalysisTag.EXPEDITIOUSNESS,
    ReviewAnalysisTag.HYGIENE,
];

type BarChartType = Extract<ChartType, 'bar'>;
@Component({
    selector: 'app-tags-bar-chart',
    templateUrl: './tags-bar-chart.component.html',
    styleUrls: ['./tags-bar-chart.component.scss'],
    imports: [NgChartsModule],
})
export class TagsBarChartComponent {
    private readonly _translateService = inject(TranslateService);

    segments = input.required<DiagnosticReviewSemanticAnalysisSegment[]>();

    readonly analysisTag = ReviewAnalysisTag;
    readonly CHART_TYPE: BarChartType = 'bar';
    displayedTags: ReviewAnalysisTag[] = DEFAULT_DISPLAYED_TAGS;

    chartDataSets = computed(() => this._computeChartData(this.segments()));
    chartLabels = computed(() => this._computeChartLabels());
    chartOption: ChartOptions<BarChartType> = this._computeChartOptions();

    private _computeChartLabels(): string[] {
        return this.displayedTags.map((tag) => this._translateService.instant(`chart.review_analysis_tags.${tag}`));
    }

    private _computeChartData(data: DiagnosticReviewSemanticAnalysisSegment[]): ChartDataset<BarChartType, ChartDataArray>[] {
        const positiveSegments = data.filter((s) => s?.sentiment === ReviewAnalysisSentiment.POSITIVE);
        const negativeSegments = data.filter((s) => s?.sentiment === ReviewAnalysisSentiment.NEGATIVE);

        const positiveTagsMap = this._buildTagsMap(positiveSegments);
        const negativeTagsMap = this._buildTagsMap(negativeSegments);

        this.displayedTags.forEach((tag) => {
            if (!positiveTagsMap[tag]?.length && !negativeTagsMap[tag]?.length) {
                this.displayedTags = this.displayedTags.filter((t) => t !== tag);
            }
        });

        this.displayedTags = this.displayedTags.filter((tag) => positiveTagsMap[tag]?.length || negativeTagsMap[tag]?.length);

        return [
            {
                borderColor: malouChartColorGreen,
                backgroundColor: malouChartColorGreen,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: this.displayedTags.map((label) => positiveTagsMap[label]?.length || 0),
            },
            {
                borderColor: malouChartColorRed,
                backgroundColor: malouChartColorRed,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: this.displayedTags.map((label) => negativeTagsMap[label]?.length || 0),
            },
        ];
    }

    private _computeChartOptions(): ChartOptions<BarChartType> {
        return {
            events: [], // Disable tooltips
            plugins: {
                legend: {
                    display: false,
                },
            },
            scales: {
                xAxis: {
                    axis: 'x',
                    type: 'category',
                    stacked: true,
                    grid: {
                        display: false,
                    },
                },
                yAxis: {
                    axis: 'y',
                    type: 'linear',
                    stacked: true,
                    grid: {
                        display: false,
                    },
                    display: false,
                },
            },
        };
    }

    private _buildTagsMap(
        segmentsWithReview: DiagnosticReviewSemanticAnalysisSegment[]
    ): PartialRecord<ReviewAnalysisTag, DiagnosticReviewSemanticAnalysisSegment[]> {
        const object = groupBy(segmentsWithReview, 'category');
        Object.values(this.analysisTag)
            .filter((tag) => tag !== ReviewAnalysisTag.OVERALL_EXPERIENCE)
            .forEach((tag) => (object[tag] = object[tag] || []));
        return object;
    }
}
