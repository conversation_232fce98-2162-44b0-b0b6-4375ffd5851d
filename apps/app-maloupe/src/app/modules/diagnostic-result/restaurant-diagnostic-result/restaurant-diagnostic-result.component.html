@if (diagnostic(); as diagnostic) {
    <div class="m-auto flex flex-col gap-y-5 px-[8vw] pb-6 md:px-6">
        <ng-container [ngTemplateOutlet]="headerTemplate"></ng-container>
        <app-restaurant-information [diagnostic]="diagnostic"></app-restaurant-information>
        <ng-container [ngTemplateOutlet]="scoreTemplate" [ngTemplateOutletContext]="{ diagnostic }"></ng-container>
        @if (diagnostic.inconsistencies; as inconsistencies) {
            <ng-container [ngTemplateOutlet]="inconsistencesTemplate" [ngTemplateOutletContext]="{ inconsistencies }"></ng-container>
        }
        <ng-container [ngTemplateOutlet]="keywordsTemplate"></ng-container>
        <ng-container [ngTemplateOutlet]="speakToAnExpertTemplate"></ng-container>
        <ng-container [ngTemplateOutlet]="competitorsTemplate"></ng-container>
        <ng-container [ngTemplateOutlet]="semanticAnalysisTemplate"></ng-container>
        @if (doesRestaurantHasInstagramData()) {
            <ng-container [ngTemplateOutlet]="instagramCompetitorsTemplate"></ng-container>
        }
        <ng-container [ngTemplateOutlet]="talkToAnExpertFooterTemplate"></ng-container>
    </div>
}

<ng-template #headerTemplate>
    <div>
        <div class="flex justify-between">
            <div class="flex">
                <div
                    class="malou-text-42--bold md:malou-text-28--bold font-passion-one uppercase text-malou-text-title md:leading-none"
                    [innerHTML]="'maloupe.diagnostic_result.header' | translate"></div>
                <mat-icon
                    class="relative bottom-2.5 right-3 !h-10 !w-10 shrink-0 text-malou-pink md:!hidden"
                    [svgIcon]="SvgIcon.SPARKLES"></mat-icon>
            </div>
            @if (isMalouUser() && publicDiagnosticUrl()) {
                <button
                    class="flex !h-12 !w-16 items-center justify-center rounded-full !text-malou-white sm:!h-10 sm:!w-20"
                    mat-flat-button
                    [ngClass]="{
                        'bg-malou-primary': !isLinkCopied(),
                        'bg-malou-primary--light': isLinkCopied(),
                    }"
                    (click)="copyLink()">
                    <mat-icon [svgIcon]="isLinkCopied() ? SvgIcon.CHECK : SvgIcon.LINK"></mat-icon>
                </button>
            }
        </div>

        <div class="malou-text-28--medium md:malou-text-14--medium text-malou-text md:mt-1">
            {{ diagnosticDate() | sentenceCase }}
        </div>
    </div>
</ng-template>

<ng-template let-diagnostic="diagnostic" #scoreTemplate>
    <app-diagnostic-score [diagnostic]="diagnostic" [isPreview]="false"></app-diagnostic-score>
</ng-template>

<ng-template let-inconsistencies="inconsistencies" #inconsistencesTemplate>
    <div class="flex flex-col gap-4 rounded-[10px] border border-malou-primary bg-malou-white p-12 md:p-4">
        <div class="malou-text-42--bold text-color-text-title md:malou-text-20--bold font-passion-one uppercase md:leading-none">
            <span [innerHTML]="'maloupe.diagnostic_result.inconsistencies.title' | pluralTranslate: inconsistencies.inconsistencyCount">
            </span>
        </div>

        <div class="flex flex-col gap-4">
            <div
                class="flex items-start justify-between rounded-[10px] p-6 md:p-4"
                [ngClass]="{ 'bg-malou-state-error-light/10': inconsistencesRating() === DiagnosticRating.BAD }">
                <div class="flex items-center">
                    <img class="h-32 md:hidden" [src]="Illustration.OK_HAND | illustrationPathResolver" />
                    <div class="flex flex-col gap-2">
                        <div class="flex justify-between gap-x-2">
                            <span class="malou-text-16--semibold md:malou-text-14--bold text-malou-text-title sm:w-9/12">
                                {{
                                    'maloupe.diagnostic_result.inconsistencies.inconsistency_count'
                                        | pluralTranslate: inconsistencies.inconsistencyCount
                                }}
                                {{
                                    'maloupe.diagnostic_result.inconsistencies.platform_count'
                                        | pluralTranslate: inconsistencies.platformCount
                                }}
                            </span>
                            <app-rating-chip class="hidden md:block" [diagnosticRating]="inconsistencesRating()"></app-rating-chip>
                        </div>
                        <div class="malou-text-12--regular flex items-center text-malou-text md:block">
                            @switch (inconsistencesRating()) {
                                @case (DiagnosticRating.GOOD) {
                                    {{ 'maloupe.diagnostic_result.inconsistencies.text.good' | translate }}
                                }
                                @default {
                                    <img class="mr-1 h-3 md:float-left" [src]="Icon.WARNING | imagePathResolver: { folder: 'icons' }" />
                                    {{ 'maloupe.diagnostic_result.inconsistencies.text.bad' | translate }}
                                }
                            }
                        </div>
                        @if (inconsistencesPlatformKeysToDisplay().length > 0) {
                            <div class="flex items-center gap-2">
                                @for (platformKey of inconsistencesPlatformKeysToDisplay(); track $index) {
                                    <img
                                        class="h-5 w-5 rounded"
                                        [alt]="platformKey"
                                        [src]="platformKey | logoPathResolver: { folder: 'platforms' }" />
                                }
                                @if (inconsistencesOtherPlatformCount() > 0) {
                                    <div class="malou-text-12--semibold text-malou-text">+{{ inconsistencesOtherPlatformCount() }}</div>
                                }
                            </div>
                        }
                    </div>
                </div>
                <app-rating-chip class="md:hidden" [diagnosticRating]="inconsistencesRating()"></app-rating-chip>
            </div>

            <div class="flex items-center gap-5 rounded-[10px] bg-malou-dark p-4">
                <img class="h-8 w-8" [alt]="Icon.TRUMPET" [src]="Icon.TRUMPET | imagePathResolver" />
                <div class="malou-text-12--bold text-malou-text">
                    {{ 'maloupe.diagnostic_result.inconsistencies.indication' | translate }}
                </div>
            </div>
            <div
                class="rounded-[10px] border border-malou-primary p-6 md:p-4"
                [ngClass]="{ 'bg-malou-state-error-light/10': photosRating() === DiagnosticRating.BAD }">
                <app-rating-bloc
                    [isInverted]="true"
                    [title]="'maloupe.diagnostic_result.inconsistencies.photos.title' | translate"
                    [text]="'maloupe.diagnostic_result.inconsistencies.photos.text' | translate"
                    [rating]="photosRating()">
                </app-rating-bloc>
            </div>
            <div
                class="rounded-[10px] border border-malou-primary p-6 md:p-4"
                [ngClass]="{ 'bg-malou-state-error-light/10': servicesRating() === DiagnosticRating.BAD }">
                <app-rating-bloc
                    [isInverted]="true"
                    [title]="'maloupe.diagnostic_result.inconsistencies.services.title' | translate"
                    [text]="
                        (presentServiceCount() === serviceCount()
                            ? 'maloupe.diagnostic_result.inconsistencies.services.text.all_present'
                            : 'maloupe.diagnostic_result.inconsistencies.services.text.some_missing'
                        ) | translate: { serviceCount: serviceCount(), presentServiceCount: presentServiceCount() }
                    "
                    [rating]="servicesRating()">
                </app-rating-bloc>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #keywordsTemplate>
    <div class="flex flex-col gap-x-7.5 rounded-[10px] border border-malou-primary bg-white p-12 md:p-4">
        <div class="flex justify-between md:gap-x-4">
            <div
                class="malou-text-42--bold md:malou-text-20--bold font-passion-one uppercase text-malou-text-title sm:w-9/12 md:leading-none"
                [innerHTML]="
                    keywordsDiagnosticRating() === DiagnosticRating.GOOD
                        ? ('maloupe.diagnostic_result.keywords.title.visible' | translate)
                        : ('maloupe.diagnostic_result.keywords.title.not_visible' | translate)
                "></div>
            <app-rating-chip [diagnosticRating]="keywordsDiagnosticRating()"></app-rating-chip>
        </div>
        <app-restaurant-diagnostic-keywords
            [keywords]="mappedKeywords()"
            [diagnosticData]="{
                diagnosticId: diagnostic()?.id ?? '',
                receiverEmail: receiverEmail() ?? '',
            }"></app-restaurant-diagnostic-keywords>
    </div>
</ng-template>

<ng-template #speakToAnExpertTemplate>
    <div class="flex items-center justify-between rounded-[10px] bg-malou-blue p-8 md:flex-col">
        <div class="flex flex-col p-5 md:gap-2 md:px-0">
            <div
                class="malou-text-35--regular font-passion-one uppercase text-white md:leading-tight"
                [innerHTML]="'maloupe.diagnostic_result.talk_to_an_expert.title_1' | translate"></div>
            <div class="malou-text-18--regular text-white">
                {{ 'maloupe.diagnostic_result.talk_to_an_expert.text_1' | translate }}
            </div>
        </div>
        <button
            class="malou-text-16--regular h-12.5 w-[220px] rounded-full px-6 py-3 text-center gradient-bg-primary md:w-full"
            (click)="talkToAnExpert()">
            {{ 'maloupe.diagnostic_result.talk_to_an_expert.cta' | translate }}
        </button>
    </div>
</ng-template>

<ng-template #competitorsTemplate>
    <div class="rounded-[10px] border border-malou-primary bg-malou-white p-8 pl-14 md:p-6">
        <div
            class="malou-text-42--bold md:malou-text-20--bold font-passion-one uppercase text-malou-text-title md:leading-tight"
            [innerHTML]="
                googleDiagnosticResult().rating === 5
                    ? ('maloupe.diagnostic_result.competitors.title.rating' | translate)
                    : ('maloupe.diagnostic_result.competitors.title.ratingCount'
                      | pluralTranslate: googleDiagnosticResult().restaurantWithHigherRatingCount)
            "></div>
        <div class="mt-12 flex w-full gap-10 md:mt-5 md:flex-col md:gap-3">
            <div class="flex w-7/12 flex-col gap-4 md:w-full">
                <div class="flex flex-col gap-4 md:gap-3">
                    <div class="flex gap-3">
                        <div class="malou-custom-box w-1/2">{{ restaurantName() }}</div>
                        <div class="malou-custom-box w-1/2">{{ 'maloupe.diagnostic_result.competitors.competitors' | translate }}</div>
                    </div>

                    <div class="flex gap-3">
                        <ng-container
                            [ngTemplateOutlet]="restaurantGoogleRatingTemplate"
                            [ngTemplateOutletContext]="{
                                googleDiagnosticRating: googleDiagnosticRating(),
                                value: googleDiagnosticResult().rating ?? '-',
                                text: googleDiagnosticResult().rating
                                    ? 'maloupe.diagnostic_result.competitors.google_rating'
                                    : 'maloupe.diagnostic_result.competitors.no_google_rating',
                            }" />
                        <ng-container
                            [ngTemplateOutlet]="competitorGoogleRatingTemplate"
                            [ngTemplateOutletContext]="{
                                value: googleDiagnosticResult().averageRatingForSimilarRestaurants,
                                text: 'maloupe.diagnostic_result.competitors.average_competitors_rating',
                            }" />
                    </div>

                    <div class="flex gap-3">
                        <ng-container
                            [ngTemplateOutlet]="restaurantGoogleRatingTemplate"
                            [ngTemplateOutletContext]="{
                                googleDiagnosticRating: googleReviewCountDiagnosticRating(),
                                value: googleDiagnosticResult().reviewCount,
                                text: 'maloupe.diagnostic_result.competitors.review_count',
                            }" />
                        <ng-container
                            [ngTemplateOutlet]="competitorGoogleRatingTemplate"
                            [ngTemplateOutletContext]="{
                                value: googleDiagnosticResult().averageReviewCountForSimilarRestaurants,
                                text: 'maloupe.diagnostic_result.competitors.average_competitors_review_count',
                            }" />
                    </div>
                </div>
            </div>
            <div
                class="flex w-1/2 flex-col items-center justify-center gap-6 rounded-xl bg-malou-primary--light px-16 md:w-full md:gap-4 md:px-5">
                <img class="w-[30%] md:w-28" [src]="Illustration.BINOCULARS | illustrationPathResolver" />
                <div class="malou-text-30--bold md:malou-text-24--bold text-center text-malou-text-title md:leading-none">
                    {{ 'maloupe.diagnostic_result.competitors.improve_rating' | translate }}
                </div>
                <button
                    class="text-malou-16--regular rounded-full bg-malou-blue px-8 py-3 text-malou-white md:mb-5"
                    (click)="redirectToReviewCalculator()">
                    {{
                        (isEnglish()
                            ? 'maloupe.diagnostic_result.competitors.get_free_session'
                            : 'maloupe.diagnostic_result.competitors.review_calculator'
                        ) | translate
                    }}
                </button>
            </div>
        </div>
        <div class="mt-6 flex items-center rounded-[10px] bg-malou-dark px-8 py-3 md:gap-3 md:px-3">
            <img class="mr-7 w-12 sm:!h-8 md:h-fit md:w-9" [alt]="Icon.TRUMPET" [src]="Icon.TRUMPET | imagePathResolver" />
            <span
                class="malou-text-12--regular mr-1 text-malou-text-title"
                [innerHTML]="'maloupe.diagnostic_result.competitors.indication' | translate">
            </span>
            @if (isEnglish()) {
                <a
                    class="malou-text-12--regular italic text-malou-text-title underline"
                    target="_blank"
                    [href]="EXPERT_DIAGNOSTIC_QUICK_CALL">
                    {{ 'maloupe.diagnostic_result.competitors.indication_action' | translate }}
                </a>
            }
        </div>
    </div>
</ng-template>

<ng-template #semanticAnalysisTemplate>
    <div class="flex flex-col gap-x-7.5 rounded-[10px] border border-malou-primary bg-white p-12 md:p-4">
        <div
            class="malou-text-42--bold md:malou-text-20--bold font-passion-one uppercase text-malou-text-title md:leading-tight"
            [innerHTML]="
                hasEnoughReviewsWithText()
                    ? ('maloupe.diagnostic_result.semantic_analysis.title.about_you' | translate)
                    : ('maloupe.diagnostic_result.semantic_analysis.title.about_your_competitors' | translate)
            "></div>
        @if (hasEnoughReviewsWithText()) {
            <div class="malou-text-12--regular md:malou-text-10--regular mb-6 italic text-malou-text-title">
                {{ 'maloupe.diagnostic_result.semantic_analysis.review_sample' | translate: { reviewCount: reviews().length } }}
            </div>
        }
        @if (shouldDisplayChart()) {
            <div class="mb-10 flex justify-around md:flex-col-reverse md:gap-2">
                @if (hasEnoughReviewsWithText() && hasEnoughCategories()) {
                    <app-tags-bar-chart [segments]="reviewSegments()"></app-tags-bar-chart>
                    <app-tags-doughnut-chart [segments]="reviewSegments()"></app-tags-doughnut-chart>
                } @else if (!hasEnoughReviewsWithText()) {
                    <div class="w-[60%]"><img alt="graph" [src]="'graph' | imagePathResolver: { folder: 'semantic-analysis' }" /></div>
                    <div class="w-[40%]">
                        <img alt="pie chart" [src]="'pie-chart' | imagePathResolver: { folder: 'semantic-analysis' }" />
                    </div>
                }
            </div>
        }
        <div class="flex items-center gap-x-7 rounded-[10px] bg-malou-dark px-8 py-3 md:gap-3 md:px-3">
            <img class="w-12 sm:!h-8 md:h-fit md:w-9" [alt]="Icon.TRUMPET" [src]="Icon.TRUMPET | imagePathResolver" />
            @if (hasEnoughReviewsWithText() && !!semanticAnalysisOverview()) {
                <span class="malou-text-12--regular text-malou-text-title">{{ semanticAnalysisOverview() }}</span>
            } @else {
                <span
                    class="malou-text-12--regular text-malou-text-title"
                    [innerHTML]="'maloupe.diagnostic_result.semantic_analysis.indication' | translate">
                </span>
            }
        </div>
        @if (!isMalouRestaurant() && shouldDisplayChart()) {
            <div class="relative">
                <button
                    class="absolute bottom-[225px] left-0 right-0 m-auto flex h-12.5 w-fit items-center rounded-full bg-malou-blue px-6 py-3 text-white"
                    (click)="talkToAnExpert()">
                    <span class="malou-text-16--medium">{{ 'maloupe.diagnostic_result.semantic_analysis.cta' | translate }}</span>
                </button>
            </div>
        }
    </div>
</ng-template>

<ng-template #instagramCompetitorsTemplate>
    <div class="rounded-[10px] border border-malou-primary bg-malou-white p-8 pl-14 md:p-6">
        <app-instagram-diagnostic-result [instagramPage]="instagramPage()" [instagramDiagnosticRating]="instagramDiagnosticRating()">
        </app-instagram-diagnostic-result>
        <div class="mt-4 flex items-center gap-4 rounded-xl bg-malou-dark px-8 py-5 md:gap-3 md:px-3">
            <img class="w-[50px] sm:!h-8 md:h-fit md:w-9" [alt]="Icon.TRUMPET" [src]="Icon.TRUMPET | imagePathResolver" />
            <div
                class="malou-text-12--regular md:malou-text-12--regular"
                [innerHTML]="'maloupe.diagnostic_result.instagram.indication' | translate"></div>
        </div>
    </div>
</ng-template>

<ng-template #talkToAnExpertFooterTemplate>
    <div class="flex rounded-[10px] bg-malou-blue p-12 pb-6 md:flex-col md:gap-4 md:overflow-hidden md:p-6 md:pb-0">
        <div class="flex w-[60%] flex-col md:w-full">
            <div
                class="malou-text-50--regular md:malou-text-34--regular font-passion-one uppercase text-white md:leading-none"
                [innerHTML]="'maloupe.diagnostic_result.talk_to_an_expert.title_2' | translate"></div>
            <div class="malou-text-18--regular mb-8 mt-6 text-white">
                {{ 'maloupe.diagnostic_result.talk_to_an_expert.text_2' | translate }}
            </div>
            <button
                class="malou-text-16--regular h-12.5 w-fit rounded-full px-6 py-3 text-center gradient-bg-primary md:w-full"
                (click)="talkToAnExpert()">
                {{ 'maloupe.diagnostic_result.talk_to_an_expert.cta' | translate }}
            </button>
        </div>
        <div class="md:h-content w-[40%] md:relative md:w-full">
            <!-- Trick to have the right div height, and use position absolute for the image to be displayed on the right -->
            <img class="invisible hidden md:block" [src]="Illustration.FINGER_BEST_FOOD_IN_TOWN | illustrationPathResolver" />
            <img class="md:absolute md:right-[-60px] md:top-0" [src]="Illustration.FINGER_BEST_FOOD_IN_TOWN | illustrationPathResolver" />
        </div>
    </div>
</ng-template>

<ng-template let-googleDiagnosticRating="googleDiagnosticRating" let-value="value" let-text="text" #restaurantGoogleRatingTemplate>
    <div
        class="flex min-h-40 w-1/2 flex-col rounded-xl p-4 pl-5 md:items-stretch md:gap-4 md:p-3"
        [ngClass]="{
            'bg-malou-pink/5': googleDiagnosticRating === DiagnosticRating.BAD,
            'border border-malou-primary bg-malou-white': googleDiagnosticRating !== DiagnosticRating.BAD,
        }">
        <div class="flex justify-end md:justify-start">
            <app-rating-chip [diagnosticRating]="googleDiagnosticRating"></app-rating-chip>
        </div>
        <div class="flex h-full flex-col justify-end pb-4">
            <span class="malou-text-40--bold leading-10">{{ value }}</span>
            <span class="malou-text-12--medium text-malou-text">
                {{ text | translate }}
            </span>
        </div>
    </div>
</ng-template>

<ng-template let-value="value" let-text="text" #competitorGoogleRatingTemplate>
    <div class="flex min-h-40 w-1/2 gap-5 rounded-xl border border-malou-primary bg-malou-white md:items-stretch">
        <div
            class="w-50px malou-text-40 flex items-center rounded-bl-xl rounded-tl-xl border border-malou-primary bg-malou-dark px-2 md:hidden">
            <img class="w-[60px]" alt="stars" [src]="'stars' | imagePathResolver" />
        </div>
        <div class="flex h-full flex-col justify-end p-3 pb-5 md:gap-2 md:py-4">
            <span class="malou-text-40--bold leading-10">
                {{ value }}
            </span>
            <span class="malou-text-12--medium text-malou-text">
                {{ text | translate }}
            </span>
        </div>
    </div>
</ng-template>
