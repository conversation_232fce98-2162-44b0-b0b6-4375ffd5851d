import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, Signal, signal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import groupBy from 'lodash.groupby';

import { DiagnosticRating, MaloupeLocale, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { DiagnosticScoreComponent } from ':modules/diagnostic-score/diagnostic-score.component';
import { RatingBlocComponent } from ':modules/rating-bloc/rating-bloc.component';
import { RatingChipComponent } from ':modules/rating-chip/rating-chip.component';
import { RestaurantDiagnosticKeywordsComponent } from ':modules/restaurant-diagnostic-keywords/restaurant-diagnostic-keywords.component';
import { RestaurantInformationComponent } from ':modules/restaurant-information/restaurant-information.component';
import { TagsBarChartComponent, TagsDoughnutChartComponent } from ':shared/components';
import { InstagramDiagnosticResultComponent } from ':shared/components/instagram-diagnostic-result/instagram-diagnostic-result.component';
import {
    AVERAGE_KEYWORD_POSITION,
    BAD_KEYWORD_POSITION,
    EXPERT_DIAGNOSTIC_QUICK_CALL,
    EXPERT_DIAGNOSTIC_REDIRECT_URL,
    EXPERT_MEET_DIAGNOSTIC_REDIRECT_URL,
    GOOD_KEYWORD_POSITION,
    MINIMUM_POST_COUNT_RECOMMENDATION,
    MINIMUM_REVIEWS_WITH_TEXT,
    MINIMUM_TAGS_COUNT,
    REVIEW_CALCULATOR_REDIRECT_FR_URL,
} from ':shared/constants';
import { Icon, MaloupePlatformKey } from ':shared/enums';
import { SvgIcon } from ':shared/enums/svg-icon.enum';
import { Diagnostic } from ':shared/models';
import {
    Illustration,
    IllustrationPathResolverPipe,
    ImagePathResolverPipe,
    LogoPathResolverPipe,
    PluralTranslatePipe,
    SentenceCasePipe,
} from ':shared/pipes';

@Component({
    selector: 'app-restaurant-diagnostic-result',
    imports: [
        NgClass,
        NgTemplateOutlet,
        DiagnosticScoreComponent,
        RatingBlocComponent,
        RatingChipComponent,
        RestaurantDiagnosticKeywordsComponent,
        RestaurantInformationComponent,
        TagsBarChartComponent,
        TagsDoughnutChartComponent,
        MatIconModule,
        MatTableModule,
        TranslateModule,
        IllustrationPathResolverPipe,
        ImagePathResolverPipe,
        ImagePathResolverPipe,
        LogoPathResolverPipe,
        PluralTranslatePipe,
        SentenceCasePipe,
        InstagramDiagnosticResultComponent,
    ],
    templateUrl: './restaurant-diagnostic-result.component.html',
    styleUrl: './restaurant-diagnostic-result.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RestaurantDiagnosticResultComponent {
    readonly diagnostic = input<Diagnostic | null>(null);
    readonly publicDiagnosticUrl = input<string | null>(null);
    readonly isMalouUser = input<boolean>(false);
    readonly receiverEmail = input<string | null>(null);

    private readonly _translateService = inject(TranslateService);

    readonly SvgIcon = SvgIcon;
    readonly PlatformKey = MaloupePlatformKey;
    readonly Illustration = Illustration;
    readonly DiagnosticRating = DiagnosticRating;
    readonly Icon = Icon;

    readonly MINIMUM_POST_COUNT_RECOMMENDATION = MINIMUM_POST_COUNT_RECOMMENDATION;
    readonly EXPERT_DIAGNOSTIC_QUICK_CALL = EXPERT_DIAGNOSTIC_QUICK_CALL;

    readonly isEnglish = signal<boolean>(false);
    readonly isLinkCopied = signal<boolean>(false);
    readonly isMalouRestaurant = computed(() => this.diagnostic()?.isMalouRestaurant ?? false);
    readonly restaurantName = computed(() => this.diagnostic()?.restaurant.name);
    readonly restaurantRating = computed(() => this.diagnostic()?.restaurant.rating);
    readonly locale: MaloupeLocale = this._translateService.currentLang as MaloupeLocale;
    readonly diagnosticDate = computed(() => {
        const createdAt = this.diagnostic()?.createdAt;
        if (!createdAt) {
            return '';
        }
        return new Date(createdAt).toLocaleString(this.locale, { month: 'long', year: 'numeric' });
    });

    readonly inconsistencesRating = computed(() => this.diagnostic()?.getInconsistencyRating() ?? DiagnosticRating.BAD);
    readonly inconsistencesPlatformKeysToDisplay = computed(() => {
        const inconsistencies = this.diagnostic()?.inconsistencies;
        if (!inconsistencies || !inconsistencies.platformKeys) {
            return [];
        }
        const platformsToDisplay: string[] = [
            MaloupePlatformKey.FACEBOOK,
            MaloupePlatformKey.TRIPADVISOR,
            MaloupePlatformKey.FOURSQUARE,
            MaloupePlatformKey.BING,
        ];
        const platformKeys = inconsistencies.platformKeys.map((key) => key.toLowerCase());
        const foundPlatforms = platformsToDisplay.filter((key) => platformKeys.includes(key));
        return [MaloupePlatformKey.GMB, MaloupePlatformKey.APPLE, ...foundPlatforms];
    });
    readonly inconsistencesOtherPlatformCount = computed(() => {
        const inconsistencies = this.diagnostic()?.inconsistencies;
        const displayedPlatforms = this.inconsistencesPlatformKeysToDisplay();
        if (!inconsistencies || inconsistencies.platformCount < displayedPlatforms.length) {
            return 0;
        }
        return inconsistencies.platformCount - displayedPlatforms.length;
    });

    readonly restaurantAverageRatingForSimilarRestaurants = computed(
        () => this.diagnostic()?.restaurant.averageRatingForSimilarRestaurants
    );
    readonly googleDiagnosticRating: Signal<DiagnosticRating> = computed(
        () => this.diagnostic()?.getGoogleRating() ?? DiagnosticRating.BAD
    );
    readonly googleReviewCountDiagnosticRating: Signal<DiagnosticRating> = computed(
        () => this.diagnostic()?.getReviewCountRating() ?? DiagnosticRating.BAD
    );

    readonly photosRating = computed(() => this.diagnostic()?.getPhotosRating() ?? DiagnosticRating.BAD);
    readonly serviceCount = computed(() => this.diagnostic()?.getServiceCount() ?? 0);
    readonly presentServiceCount = computed(() => this.diagnostic()?.getPresentServiceCount() ?? 0);
    readonly servicesRating = computed(() => this.diagnostic()?.getServicesRating() ?? DiagnosticRating.BAD);

    readonly chosenKeywords = computed(() => this.diagnostic()?.getKeywords() ?? []);
    readonly mappedKeywords = computed(() => {
        const chosenKeywords = this.chosenKeywords();
        return chosenKeywords.map((keyword) => ({
            keyword: keyword,
            rating: this._getRatingFromGooglePosition(keyword.googlePosition),
            displayedRating: this._getDisplayedRatingFromGooglePosition(keyword.googlePosition),
            text: keyword.keyword[this.locale],
            restaurantName: this.diagnostic()?.restaurant.name ?? '',
            address: this.diagnostic()?.restaurant.address.formattedAddress ?? '',
            placeId: this.diagnostic()?.placeId ?? '',
        }));
    });
    readonly keywordsDiagnosticRating = computed(() => this.diagnostic()?.getKeywordsRating() ?? DiagnosticRating.BAD);

    readonly googleDiagnosticResult = computed(() => ({
        rating: this.diagnostic()?.restaurant?.rating,
        reviewCount: this.diagnostic()?.reviewCount,
        averageReviewCountForSimilarRestaurants: this.diagnostic()?.averageReviewCountForSimilarRestaurants,
        averageRatingForSimilarRestaurants: this.diagnostic()?.restaurant?.averageRatingForSimilarRestaurants,
        restaurantWithHigherRatingCount: this.diagnostic()?.getBetterLocalCompetitorCount() ?? 0,
    }));

    readonly reviews = computed(() => this.diagnostic()?.reviews ?? []);
    readonly reviewSegments = computed(
        () =>
            this.reviews()
                .flatMap((review) => review.semanticAnalysisSegments)
                .filter((segment) => {
                    const isNeutral = segment.sentiment === ReviewAnalysisSentiment.NEUTRAL;
                    const isOverallExperience = segment.category === ReviewAnalysisTag.OVERALL_EXPERIENCE;
                    return !isNeutral && !isOverallExperience;
                }) ?? []
    );
    readonly hasEnoughCategories = computed(() => Object.keys(groupBy(this.reviewSegments(), 'category')).length >= MINIMUM_TAGS_COUNT);
    readonly hasEnoughReviewsWithText = computed(() => this.reviews().length >= MINIMUM_REVIEWS_WITH_TEXT);
    readonly shouldDisplayChart = computed(() => {
        if (this.hasEnoughReviewsWithText()) {
            return this.hasEnoughCategories();
        }
        return true;
    });
    readonly semanticAnalysisOverview = computed(() => {
        const lang = this.locale;
        return this.diagnostic()?.semanticAnalysisOverviewByLang?.[lang];
    });

    readonly instagramPage = computed(() => this.diagnostic()?.instagramPage);
    readonly instagramDiagnosticRating: Signal<
        | {
              followersDiagnosticRating: DiagnosticRating;
              likesDiagnosticRating: DiagnosticRating;
              postCountDiagnosticRating: DiagnosticRating;
          }
        | undefined
    > = computed(() => this.diagnostic()?.getInstagramRating());

    readonly doesRestaurantHasInstagramData = computed(() => !!this.diagnostic()?.instagramPage?.name);

    constructor() {
        this.isEnglish.set(this._translateService.currentLang === MaloupeLocale.EN);
    }

    talkToAnExpert(): void {
        window.open(EXPERT_DIAGNOSTIC_REDIRECT_URL, '_blank');
    }

    redirectToReviewCalculator(): void {
        // Temporary solution to redirect to the quick call with an expert until the english review calculator is ready
        const url = this.locale === MaloupeLocale.EN ? EXPERT_MEET_DIAGNOSTIC_REDIRECT_URL : REVIEW_CALCULATOR_REDIRECT_FR_URL;
        window.open(url, '_blank');
    }

    copyLink(): void {
        const publicDiagnosticUrl = this.publicDiagnosticUrl();
        if (publicDiagnosticUrl) {
            navigator.clipboard.writeText(publicDiagnosticUrl).then(() => {
                this.isLinkCopied.set(true);
            });
        }
    }

    private _getRatingFromGooglePosition(googlePosition: number): DiagnosticRating {
        if (BAD_KEYWORD_POSITION.includes(googlePosition)) {
            return DiagnosticRating.BAD;
        }
        if (AVERAGE_KEYWORD_POSITION.includes(googlePosition)) {
            return DiagnosticRating.AVERAGE;
        }
        if (GOOD_KEYWORD_POSITION.includes(googlePosition)) {
            return DiagnosticRating.GOOD;
        }
        return DiagnosticRating.VERY_BAD;
    }

    private _getDisplayedRatingFromGooglePosition(googlePosition: number): string {
        if (googlePosition >= 20) {
            return '+20';
        }
        return googlePosition.toString();
    }
}
